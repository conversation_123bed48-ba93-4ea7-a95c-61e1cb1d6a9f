('D:\\testcode\\typegame改\\build\\TypingContest\\PYZ-00.pyz',
 [('IPython',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\__init__.py',
   'PYMODULE'),
  ('IPython.core',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\__init__.py',
   'PYMODULE'),
  ('IPython.core.alias',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\alias.py',
   'PYMODULE'),
  ('IPython.core.application',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\application.py',
   'PYMODULE'),
  ('IPython.core.async_helpers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\async_helpers.py',
   'PYMODULE'),
  ('IPython.core.autocall',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\autocall.py',
   'PYMODULE'),
  ('IPython.core.builtin_trap',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\builtin_trap.py',
   'PYMODULE'),
  ('IPython.core.compilerop',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\compilerop.py',
   'PYMODULE'),
  ('IPython.core.completer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\completer.py',
   'PYMODULE'),
  ('IPython.core.completerlib',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\completerlib.py',
   'PYMODULE'),
  ('IPython.core.crashhandler',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\crashhandler.py',
   'PYMODULE'),
  ('IPython.core.debugger',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\debugger.py',
   'PYMODULE'),
  ('IPython.core.display',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\display.py',
   'PYMODULE'),
  ('IPython.core.display_functions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\display_functions.py',
   'PYMODULE'),
  ('IPython.core.display_trap',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\display_trap.py',
   'PYMODULE'),
  ('IPython.core.displayhook',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\displayhook.py',
   'PYMODULE'),
  ('IPython.core.displaypub',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\displaypub.py',
   'PYMODULE'),
  ('IPython.core.error',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\error.py',
   'PYMODULE'),
  ('IPython.core.events',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\events.py',
   'PYMODULE'),
  ('IPython.core.excolors',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\excolors.py',
   'PYMODULE'),
  ('IPython.core.extensions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\extensions.py',
   'PYMODULE'),
  ('IPython.core.formatters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\formatters.py',
   'PYMODULE'),
  ('IPython.core.getipython',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\getipython.py',
   'PYMODULE'),
  ('IPython.core.guarded_eval',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\guarded_eval.py',
   'PYMODULE'),
  ('IPython.core.history',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\history.py',
   'PYMODULE'),
  ('IPython.core.hooks',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\hooks.py',
   'PYMODULE'),
  ('IPython.core.inputtransformer2',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\inputtransformer2.py',
   'PYMODULE'),
  ('IPython.core.interactiveshell',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\interactiveshell.py',
   'PYMODULE'),
  ('IPython.core.latex_symbols',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\latex_symbols.py',
   'PYMODULE'),
  ('IPython.core.logger',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\logger.py',
   'PYMODULE'),
  ('IPython.core.macro',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\macro.py',
   'PYMODULE'),
  ('IPython.core.magic',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\magic.py',
   'PYMODULE'),
  ('IPython.core.magic_arguments',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\magic_arguments.py',
   'PYMODULE'),
  ('IPython.core.magics',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\magics\\__init__.py',
   'PYMODULE'),
  ('IPython.core.magics.ast_mod',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\magics\\ast_mod.py',
   'PYMODULE'),
  ('IPython.core.magics.auto',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\magics\\auto.py',
   'PYMODULE'),
  ('IPython.core.magics.basic',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\magics\\basic.py',
   'PYMODULE'),
  ('IPython.core.magics.code',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\magics\\code.py',
   'PYMODULE'),
  ('IPython.core.magics.config',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\magics\\config.py',
   'PYMODULE'),
  ('IPython.core.magics.display',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\magics\\display.py',
   'PYMODULE'),
  ('IPython.core.magics.execution',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\magics\\execution.py',
   'PYMODULE'),
  ('IPython.core.magics.extension',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\magics\\extension.py',
   'PYMODULE'),
  ('IPython.core.magics.history',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\magics\\history.py',
   'PYMODULE'),
  ('IPython.core.magics.logging',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\magics\\logging.py',
   'PYMODULE'),
  ('IPython.core.magics.namespace',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\magics\\namespace.py',
   'PYMODULE'),
  ('IPython.core.magics.osm',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\magics\\osm.py',
   'PYMODULE'),
  ('IPython.core.magics.packaging',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\magics\\packaging.py',
   'PYMODULE'),
  ('IPython.core.magics.pylab',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\magics\\pylab.py',
   'PYMODULE'),
  ('IPython.core.magics.script',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\magics\\script.py',
   'PYMODULE'),
  ('IPython.core.oinspect',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\oinspect.py',
   'PYMODULE'),
  ('IPython.core.page',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\page.py',
   'PYMODULE'),
  ('IPython.core.payload',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\payload.py',
   'PYMODULE'),
  ('IPython.core.payloadpage',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\payloadpage.py',
   'PYMODULE'),
  ('IPython.core.prefilter',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\prefilter.py',
   'PYMODULE'),
  ('IPython.core.profiledir',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\profiledir.py',
   'PYMODULE'),
  ('IPython.core.pylabtools',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\pylabtools.py',
   'PYMODULE'),
  ('IPython.core.release',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\release.py',
   'PYMODULE'),
  ('IPython.core.shellapp',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\shellapp.py',
   'PYMODULE'),
  ('IPython.core.splitinput',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\splitinput.py',
   'PYMODULE'),
  ('IPython.core.ultratb',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\ultratb.py',
   'PYMODULE'),
  ('IPython.core.usage',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\core\\usage.py',
   'PYMODULE'),
  ('IPython.display',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\display.py',
   'PYMODULE'),
  ('IPython.extensions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\extensions\\__init__.py',
   'PYMODULE'),
  ('IPython.extensions.storemagic',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\extensions\\storemagic.py',
   'PYMODULE'),
  ('IPython.external',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\external\\__init__.py',
   'PYMODULE'),
  ('IPython.external.qt_for_kernel',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\external\\qt_for_kernel.py',
   'PYMODULE'),
  ('IPython.external.qt_loaders',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\external\\qt_loaders.py',
   'PYMODULE'),
  ('IPython.lib',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\lib\\__init__.py',
   'PYMODULE'),
  ('IPython.lib.clipboard',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\lib\\clipboard.py',
   'PYMODULE'),
  ('IPython.lib.display',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\lib\\display.py',
   'PYMODULE'),
  ('IPython.lib.guisupport',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\lib\\guisupport.py',
   'PYMODULE'),
  ('IPython.lib.pretty',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\lib\\pretty.py',
   'PYMODULE'),
  ('IPython.paths',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\paths.py',
   'PYMODULE'),
  ('IPython.terminal',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\terminal\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.debugger',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\terminal\\debugger.py',
   'PYMODULE'),
  ('IPython.terminal.embed',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\terminal\\embed.py',
   'PYMODULE'),
  ('IPython.terminal.interactiveshell',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\terminal\\interactiveshell.py',
   'PYMODULE'),
  ('IPython.terminal.ipapp',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\terminal\\ipapp.py',
   'PYMODULE'),
  ('IPython.terminal.magics',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\terminal\\magics.py',
   'PYMODULE'),
  ('IPython.terminal.prompts',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\terminal\\prompts.py',
   'PYMODULE'),
  ('IPython.terminal.pt_inputhooks',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\terminal\\pt_inputhooks\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.ptutils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\terminal\\ptutils.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\terminal\\shortcuts\\__init__.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.auto_match',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\terminal\\shortcuts\\auto_match.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.auto_suggest',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\terminal\\shortcuts\\auto_suggest.py',
   'PYMODULE'),
  ('IPython.terminal.shortcuts.filters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\terminal\\shortcuts\\filters.py',
   'PYMODULE'),
  ('IPython.testing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\testing\\__init__.py',
   'PYMODULE'),
  ('IPython.testing.skipdoctest',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\testing\\skipdoctest.py',
   'PYMODULE'),
  ('IPython.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\__init__.py',
   'PYMODULE'),
  ('IPython.utils.PyColorize',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\PyColorize.py',
   'PYMODULE'),
  ('IPython.utils._process_cli',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\_process_cli.py',
   'PYMODULE'),
  ('IPython.utils._process_common',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\_process_common.py',
   'PYMODULE'),
  ('IPython.utils._process_emscripten',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\_process_emscripten.py',
   'PYMODULE'),
  ('IPython.utils._process_posix',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\_process_posix.py',
   'PYMODULE'),
  ('IPython.utils._process_win32',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\_process_win32.py',
   'PYMODULE'),
  ('IPython.utils._sysinfo',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\_sysinfo.py',
   'PYMODULE'),
  ('IPython.utils.capture',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\capture.py',
   'PYMODULE'),
  ('IPython.utils.colorable',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\colorable.py',
   'PYMODULE'),
  ('IPython.utils.coloransi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\coloransi.py',
   'PYMODULE'),
  ('IPython.utils.contexts',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\contexts.py',
   'PYMODULE'),
  ('IPython.utils.data',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\data.py',
   'PYMODULE'),
  ('IPython.utils.decorators',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\decorators.py',
   'PYMODULE'),
  ('IPython.utils.dir2',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\dir2.py',
   'PYMODULE'),
  ('IPython.utils.docs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\docs.py',
   'PYMODULE'),
  ('IPython.utils.encoding',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\encoding.py',
   'PYMODULE'),
  ('IPython.utils.frame',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\frame.py',
   'PYMODULE'),
  ('IPython.utils.generics',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\generics.py',
   'PYMODULE'),
  ('IPython.utils.importstring',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\importstring.py',
   'PYMODULE'),
  ('IPython.utils.io',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\io.py',
   'PYMODULE'),
  ('IPython.utils.ipstruct',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\ipstruct.py',
   'PYMODULE'),
  ('IPython.utils.module_paths',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\module_paths.py',
   'PYMODULE'),
  ('IPython.utils.openpy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\openpy.py',
   'PYMODULE'),
  ('IPython.utils.path',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\path.py',
   'PYMODULE'),
  ('IPython.utils.process',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\process.py',
   'PYMODULE'),
  ('IPython.utils.py3compat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\py3compat.py',
   'PYMODULE'),
  ('IPython.utils.sentinel',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\sentinel.py',
   'PYMODULE'),
  ('IPython.utils.strdispatch',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\strdispatch.py',
   'PYMODULE'),
  ('IPython.utils.sysinfo',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\sysinfo.py',
   'PYMODULE'),
  ('IPython.utils.syspathcontext',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\syspathcontext.py',
   'PYMODULE'),
  ('IPython.utils.terminal',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\terminal.py',
   'PYMODULE'),
  ('IPython.utils.text',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\text.py',
   'PYMODULE'),
  ('IPython.utils.timing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\timing.py',
   'PYMODULE'),
  ('IPython.utils.tokenutil',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\tokenutil.py',
   'PYMODULE'),
  ('IPython.utils.wildcard',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\IPython\\utils\\wildcard.py',
   'PYMODULE'),
  ('PIL',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_osx_support',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyio',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\_pyio.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\_threading_local.py',
   'PYMODULE'),
  ('anyio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\__init__.py',
   'PYMODULE'),
  ('anyio._backends',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\_backends\\__init__.py',
   'PYMODULE'),
  ('anyio._backends._asyncio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\_backends\\_asyncio.py',
   'PYMODULE'),
  ('anyio._backends._trio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\_backends\\_trio.py',
   'PYMODULE'),
  ('anyio._core',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\_core\\__init__.py',
   'PYMODULE'),
  ('anyio._core._asyncio_selector_thread',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\_core\\_asyncio_selector_thread.py',
   'PYMODULE'),
  ('anyio._core._eventloop',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\_core\\_eventloop.py',
   'PYMODULE'),
  ('anyio._core._exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('anyio._core._fileio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\_core\\_fileio.py',
   'PYMODULE'),
  ('anyio._core._resources',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\_core\\_resources.py',
   'PYMODULE'),
  ('anyio._core._signals',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\_core\\_signals.py',
   'PYMODULE'),
  ('anyio._core._sockets',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\_core\\_sockets.py',
   'PYMODULE'),
  ('anyio._core._streams',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\_core\\_streams.py',
   'PYMODULE'),
  ('anyio._core._subprocesses',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\_core\\_subprocesses.py',
   'PYMODULE'),
  ('anyio._core._synchronization',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\_core\\_synchronization.py',
   'PYMODULE'),
  ('anyio._core._tasks',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\_core\\_tasks.py',
   'PYMODULE'),
  ('anyio._core._testing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\_core\\_testing.py',
   'PYMODULE'),
  ('anyio._core._typedattr',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\_core\\_typedattr.py',
   'PYMODULE'),
  ('anyio.abc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\abc\\__init__.py',
   'PYMODULE'),
  ('anyio.abc._eventloop',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\abc\\_eventloop.py',
   'PYMODULE'),
  ('anyio.abc._resources',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\abc\\_resources.py',
   'PYMODULE'),
  ('anyio.abc._sockets',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\abc\\_sockets.py',
   'PYMODULE'),
  ('anyio.abc._streams',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\abc\\_streams.py',
   'PYMODULE'),
  ('anyio.abc._subprocesses',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\abc\\_subprocesses.py',
   'PYMODULE'),
  ('anyio.abc._tasks',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\abc\\_tasks.py',
   'PYMODULE'),
  ('anyio.abc._testing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\abc\\_testing.py',
   'PYMODULE'),
  ('anyio.from_thread',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\from_thread.py',
   'PYMODULE'),
  ('anyio.lowlevel',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\lowlevel.py',
   'PYMODULE'),
  ('anyio.streams',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\streams\\__init__.py',
   'PYMODULE'),
  ('anyio.streams.memory',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\streams\\memory.py',
   'PYMODULE'),
  ('anyio.streams.stapled',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\streams\\stapled.py',
   'PYMODULE'),
  ('anyio.streams.tls',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\streams\\tls.py',
   'PYMODULE'),
  ('anyio.to_thread',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\anyio\\to_thread.py',
   'PYMODULE'),
  ('argparse',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\ast.py',
   'PYMODULE'),
  ('asttokens',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\asttokens\\__init__.py',
   'PYMODULE'),
  ('asttokens.astroid_compat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\asttokens\\astroid_compat.py',
   'PYMODULE'),
  ('asttokens.asttokens',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\asttokens\\asttokens.py',
   'PYMODULE'),
  ('asttokens.line_numbers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\asttokens\\line_numbers.py',
   'PYMODULE'),
  ('asttokens.mark_tokens',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\asttokens\\mark_tokens.py',
   'PYMODULE'),
  ('asttokens.util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\asttokens\\util.py',
   'PYMODULE'),
  ('async_timeout',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\async_timeout\\__init__.py',
   'PYMODULE'),
  ('asyncio',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attrs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\attrs\\__init__.py',
   'PYMODULE'),
  ('attrs.converters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\attrs\\converters.py',
   'PYMODULE'),
  ('attrs.exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\attrs\\exceptions.py',
   'PYMODULE'),
  ('attrs.filters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\attrs\\filters.py',
   'PYMODULE'),
  ('attrs.setters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\attrs\\setters.py',
   'PYMODULE'),
  ('attrs.validators',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\attrs\\validators.py',
   'PYMODULE'),
  ('backports',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\bdb.py',
   'PYMODULE'),
  ('bidict',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\bidict\\__init__.py',
   'PYMODULE'),
  ('bidict._abc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\bidict\\_abc.py',
   'PYMODULE'),
  ('bidict._base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\bidict\\_base.py',
   'PYMODULE'),
  ('bidict._bidict',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\bidict\\_bidict.py',
   'PYMODULE'),
  ('bidict._dup',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\bidict\\_dup.py',
   'PYMODULE'),
  ('bidict._exc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\bidict\\_exc.py',
   'PYMODULE'),
  ('bidict._frozen',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\bidict\\_frozen.py',
   'PYMODULE'),
  ('bidict._iter',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\bidict\\_iter.py',
   'PYMODULE'),
  ('bidict._orderedbase',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\bidict\\_orderedbase.py',
   'PYMODULE'),
  ('bidict._orderedbidict',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\bidict\\_orderedbidict.py',
   'PYMODULE'),
  ('bidict._typing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\bidict\\_typing.py',
   'PYMODULE'),
  ('bidict.metadata',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\bidict\\metadata.py',
   'PYMODULE'),
  ('bisect',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\bisect.py',
   'PYMODULE'),
  ('blinker',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker._utilities',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('bz2',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\bz2.py',
   'PYMODULE'),
  ('cProfile',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\cProfile.py',
   'PYMODULE'),
  ('calendar',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\cgi.py',
   'PYMODULE'),
  ('charset_normalizer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('chunk',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\chunk.py',
   'PYMODULE'),
  ('click',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.testing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.types',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('cloudpickle',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\cloudpickle\\__init__.py',
   'PYMODULE'),
  ('cloudpickle.cloudpickle',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\cloudpickle\\cloudpickle.py',
   'PYMODULE'),
  ('cloudpickle.cloudpickle_fast',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\cloudpickle\\cloudpickle_fast.py',
   'PYMODULE'),
  ('clr',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\clr.py',
   'PYMODULE'),
  ('clr_loader',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\clr_loader\\__init__.py',
   'PYMODULE'),
  ('clr_loader.ffi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\clr_loader\\ffi\\__init__.py',
   'PYMODULE'),
  ('clr_loader.ffi.hostfxr',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\clr_loader\\ffi\\hostfxr.py',
   'PYMODULE'),
  ('clr_loader.ffi.mono',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\clr_loader\\ffi\\mono.py',
   'PYMODULE'),
  ('clr_loader.ffi.netfx',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\clr_loader\\ffi\\netfx.py',
   'PYMODULE'),
  ('clr_loader.hostfxr',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\clr_loader\\hostfxr.py',
   'PYMODULE'),
  ('clr_loader.mono',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\clr_loader\\mono.py',
   'PYMODULE'),
  ('clr_loader.netfx',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\clr_loader\\netfx.py',
   'PYMODULE'),
  ('clr_loader.types',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\clr_loader\\types.py',
   'PYMODULE'),
  ('clr_loader.util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\clr_loader\\util\\__init__.py',
   'PYMODULE'),
  ('clr_loader.util.clr_error',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\clr_loader\\util\\clr_error.py',
   'PYMODULE'),
  ('clr_loader.util.coreclr_errors',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\clr_loader\\util\\coreclr_errors.py',
   'PYMODULE'),
  ('clr_loader.util.find',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\clr_loader\\util\\find.py',
   'PYMODULE'),
  ('clr_loader.util.hostfxr_errors',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\clr_loader\\util\\hostfxr_errors.py',
   'PYMODULE'),
  ('clr_loader.util.runtime_spec',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\clr_loader\\util\\runtime_spec.py',
   'PYMODULE'),
  ('cmd',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\codeop.py',
   'PYMODULE'),
  ('colorama',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\colorsys.py',
   'PYMODULE'),
  ('comm',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\comm\\__init__.py',
   'PYMODULE'),
  ('comm.base_comm',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\comm\\base_comm.py',
   'PYMODULE'),
  ('concurrent',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('curses',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\curses\\has_key.py',
   'PYMODULE'),
  ('dataclasses',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\datetime.py',
   'PYMODULE'),
  ('dateutil',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('debugpy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\debugpy\\__init__.py',
   'PYMODULE'),
  ('debugpy._vendored',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\debugpy\\_vendored\\__init__.py',
   'PYMODULE'),
  ('debugpy._vendored._util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\debugpy\\_vendored\\_util.py',
   'PYMODULE'),
  ('debugpy._vendored.force_pydevd',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\debugpy\\_vendored\\force_pydevd.py',
   'PYMODULE'),
  ('debugpy._version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\debugpy\\_version.py',
   'PYMODULE'),
  ('debugpy.adapter',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\debugpy\\adapter\\__init__.py',
   'PYMODULE'),
  ('debugpy.common',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\debugpy\\common\\__init__.py',
   'PYMODULE'),
  ('debugpy.common.json',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\debugpy\\common\\json.py',
   'PYMODULE'),
  ('debugpy.common.log',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\debugpy\\common\\log.py',
   'PYMODULE'),
  ('debugpy.common.sockets',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\debugpy\\common\\sockets.py',
   'PYMODULE'),
  ('debugpy.common.timestamp',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\debugpy\\common\\timestamp.py',
   'PYMODULE'),
  ('debugpy.common.util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\debugpy\\common\\util.py',
   'PYMODULE'),
  ('debugpy.public_api',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\debugpy\\public_api.py',
   'PYMODULE'),
  ('debugpy.server',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\debugpy\\server\\__init__.py',
   'PYMODULE'),
  ('debugpy.server.api',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\debugpy\\server\\api.py',
   'PYMODULE'),
  ('decimal',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\decimal.py',
   'PYMODULE'),
  ('decorator',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\decorator.py',
   'PYMODULE'),
  ('difflib',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\dis.py',
   'PYMODULE'),
  ('distutils',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.util',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('dns',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\__init__.py',
   'PYMODULE'),
  ('dns._asyncbackend',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\_asyncbackend.py',
   'PYMODULE'),
  ('dns._asyncio_backend',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\_asyncio_backend.py',
   'PYMODULE'),
  ('dns._ddr',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\_ddr.py',
   'PYMODULE'),
  ('dns._features',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\_features.py',
   'PYMODULE'),
  ('dns._immutable_ctx',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\_immutable_ctx.py',
   'PYMODULE'),
  ('dns._trio_backend',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\_trio_backend.py',
   'PYMODULE'),
  ('dns.asyncbackend',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\asyncbackend.py',
   'PYMODULE'),
  ('dns.asyncquery',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\asyncquery.py',
   'PYMODULE'),
  ('dns.asyncresolver',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\asyncresolver.py',
   'PYMODULE'),
  ('dns.dnssec',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\dnssec.py',
   'PYMODULE'),
  ('dns.dnssecalgs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\dnssecalgs\\__init__.py',
   'PYMODULE'),
  ('dns.dnssecalgs.base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\dnssecalgs\\base.py',
   'PYMODULE'),
  ('dns.dnssecalgs.cryptography',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\dnssecalgs\\cryptography.py',
   'PYMODULE'),
  ('dns.dnssecalgs.dsa',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\dnssecalgs\\dsa.py',
   'PYMODULE'),
  ('dns.dnssecalgs.ecdsa',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\dnssecalgs\\ecdsa.py',
   'PYMODULE'),
  ('dns.dnssecalgs.eddsa',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\dnssecalgs\\eddsa.py',
   'PYMODULE'),
  ('dns.dnssecalgs.rsa',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\dnssecalgs\\rsa.py',
   'PYMODULE'),
  ('dns.dnssectypes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\dnssectypes.py',
   'PYMODULE'),
  ('dns.e164',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\e164.py',
   'PYMODULE'),
  ('dns.edns',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\edns.py',
   'PYMODULE'),
  ('dns.entropy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\entropy.py',
   'PYMODULE'),
  ('dns.enum',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\enum.py',
   'PYMODULE'),
  ('dns.exception',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\exception.py',
   'PYMODULE'),
  ('dns.flags',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\flags.py',
   'PYMODULE'),
  ('dns.grange',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\grange.py',
   'PYMODULE'),
  ('dns.immutable',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\immutable.py',
   'PYMODULE'),
  ('dns.inet',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\inet.py',
   'PYMODULE'),
  ('dns.ipv4',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\ipv4.py',
   'PYMODULE'),
  ('dns.ipv6',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\ipv6.py',
   'PYMODULE'),
  ('dns.message',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\message.py',
   'PYMODULE'),
  ('dns.name',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\name.py',
   'PYMODULE'),
  ('dns.namedict',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\namedict.py',
   'PYMODULE'),
  ('dns.nameserver',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\nameserver.py',
   'PYMODULE'),
  ('dns.node',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\node.py',
   'PYMODULE'),
  ('dns.opcode',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\opcode.py',
   'PYMODULE'),
  ('dns.query',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\query.py',
   'PYMODULE'),
  ('dns.quic',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\quic\\__init__.py',
   'PYMODULE'),
  ('dns.quic._asyncio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\quic\\_asyncio.py',
   'PYMODULE'),
  ('dns.quic._common',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\quic\\_common.py',
   'PYMODULE'),
  ('dns.quic._sync',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\quic\\_sync.py',
   'PYMODULE'),
  ('dns.quic._trio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\quic\\_trio.py',
   'PYMODULE'),
  ('dns.rcode',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rcode.py',
   'PYMODULE'),
  ('dns.rdata',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdata.py',
   'PYMODULE'),
  ('dns.rdataclass',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdataclass.py',
   'PYMODULE'),
  ('dns.rdataset',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdataset.py',
   'PYMODULE'),
  ('dns.rdatatype',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdatatype.py',
   'PYMODULE'),
  ('dns.rdtypes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AFSDB',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\AFSDB.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AMTRELAY',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\AMTRELAY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.AVC',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\AVC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CAA',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\CAA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CDNSKEY',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\CDNSKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CDS',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\CDS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CERT',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\CERT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CNAME',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\CNAME.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.CSYNC',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\CSYNC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DLV',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\DLV.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DNAME',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\DNAME.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DNSKEY',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\DNSKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.DS',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\DS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.EUI48',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\EUI48.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.EUI64',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\EUI64.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.GPOS',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\GPOS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.HINFO',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\HINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.HIP',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\HIP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.ISDN',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\ISDN.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.L32',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\L32.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.L64',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\L64.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.LOC',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\LOC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.LP',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\LP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.MX',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\MX.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NID',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\NID.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NINFO',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\NINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NS',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\NS.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\NSEC.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC3',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\NSEC3.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.NSEC3PARAM',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\NSEC3PARAM.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.OPENPGPKEY',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\OPENPGPKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.OPT',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\OPT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.PTR',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\PTR.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RESINFO',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\RESINFO.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RP',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\RP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RRSIG',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\RRSIG.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.RT',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\RT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SMIMEA',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\SMIMEA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SOA',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\SOA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SPF',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\SPF.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.SSHFP',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\SSHFP.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TKEY',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\TKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TLSA',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\TLSA.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TSIG',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\TSIG.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.TXT',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\TXT.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.URI',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\URI.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.WALLET',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\WALLET.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.X25',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\X25.py',
   'PYMODULE'),
  ('dns.rdtypes.ANY.ZONEMD',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\ANY\\ZONEMD.py',
   'PYMODULE'),
  ('dns.rdtypes.CH',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\CH\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.CH.A',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\CH\\A.py',
   'PYMODULE'),
  ('dns.rdtypes.IN',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\IN\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.A',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\IN\\A.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.AAAA',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\IN\\AAAA.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.APL',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\IN\\APL.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.DHCID',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\IN\\DHCID.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.HTTPS',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\IN\\HTTPS.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.IPSECKEY',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\IN\\IPSECKEY.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.KX',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\IN\\KX.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NAPTR',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\IN\\NAPTR.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NSAP',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\IN\\NSAP.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.NSAP_PTR',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\IN\\NSAP_PTR.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.PX',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\IN\\PX.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.SRV',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\IN\\SRV.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.SVCB',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\IN\\SVCB.py',
   'PYMODULE'),
  ('dns.rdtypes.IN.WKS',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\IN\\WKS.py',
   'PYMODULE'),
  ('dns.rdtypes.dnskeybase',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\dnskeybase.py',
   'PYMODULE'),
  ('dns.rdtypes.dsbase',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\dsbase.py',
   'PYMODULE'),
  ('dns.rdtypes.euibase',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\euibase.py',
   'PYMODULE'),
  ('dns.rdtypes.mxbase',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\mxbase.py',
   'PYMODULE'),
  ('dns.rdtypes.nsbase',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\nsbase.py',
   'PYMODULE'),
  ('dns.rdtypes.svcbbase',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\svcbbase.py',
   'PYMODULE'),
  ('dns.rdtypes.tlsabase',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\tlsabase.py',
   'PYMODULE'),
  ('dns.rdtypes.txtbase',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\txtbase.py',
   'PYMODULE'),
  ('dns.rdtypes.util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rdtypes\\util.py',
   'PYMODULE'),
  ('dns.renderer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\renderer.py',
   'PYMODULE'),
  ('dns.resolver',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\resolver.py',
   'PYMODULE'),
  ('dns.reversename',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\reversename.py',
   'PYMODULE'),
  ('dns.rrset',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\rrset.py',
   'PYMODULE'),
  ('dns.serial',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\serial.py',
   'PYMODULE'),
  ('dns.set',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\set.py',
   'PYMODULE'),
  ('dns.tokenizer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\tokenizer.py',
   'PYMODULE'),
  ('dns.transaction',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\transaction.py',
   'PYMODULE'),
  ('dns.tsig',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\tsig.py',
   'PYMODULE'),
  ('dns.tsigkeyring',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\tsigkeyring.py',
   'PYMODULE'),
  ('dns.ttl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\ttl.py',
   'PYMODULE'),
  ('dns.update',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\update.py',
   'PYMODULE'),
  ('dns.version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\version.py',
   'PYMODULE'),
  ('dns.versioned',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\versioned.py',
   'PYMODULE'),
  ('dns.win32util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\win32util.py',
   'PYMODULE'),
  ('dns.wire',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\wire.py',
   'PYMODULE'),
  ('dns.xfr',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\xfr.py',
   'PYMODULE'),
  ('dns.zone',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\zone.py',
   'PYMODULE'),
  ('dns.zonefile',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\zonefile.py',
   'PYMODULE'),
  ('dns.zonetypes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dns\\zonetypes.py',
   'PYMODULE'),
  ('doctest',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\doctest.py',
   'PYMODULE'),
  ('dotenv',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\email\\utils.py',
   'PYMODULE'),
  ('engineio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\__init__.py',
   'PYMODULE'),
  ('engineio.async_drivers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\async_drivers\\__init__.py',
   'PYMODULE'),
  ('engineio.async_drivers.aiohttp',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\async_drivers\\aiohttp.py',
   'PYMODULE'),
  ('engineio.async_drivers.asgi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\async_drivers\\asgi.py',
   'PYMODULE'),
  ('engineio.async_drivers.eventlet',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\async_drivers\\eventlet.py',
   'PYMODULE'),
  ('engineio.async_drivers.gevent',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\async_drivers\\gevent.py',
   'PYMODULE'),
  ('engineio.async_drivers.gevent_uwsgi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\async_drivers\\gevent_uwsgi.py',
   'PYMODULE'),
  ('engineio.async_drivers.sanic',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\async_drivers\\sanic.py',
   'PYMODULE'),
  ('engineio.async_drivers.threading',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\async_drivers\\threading.py',
   'PYMODULE'),
  ('engineio.async_drivers.tornado',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\async_drivers\\tornado.py',
   'PYMODULE'),
  ('engineio.asyncio_client',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\asyncio_client.py',
   'PYMODULE'),
  ('engineio.asyncio_server',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\asyncio_server.py',
   'PYMODULE'),
  ('engineio.asyncio_socket',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\asyncio_socket.py',
   'PYMODULE'),
  ('engineio.client',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\client.py',
   'PYMODULE'),
  ('engineio.exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\exceptions.py',
   'PYMODULE'),
  ('engineio.json',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\json.py',
   'PYMODULE'),
  ('engineio.middleware',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\middleware.py',
   'PYMODULE'),
  ('engineio.packet',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\packet.py',
   'PYMODULE'),
  ('engineio.payload',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\payload.py',
   'PYMODULE'),
  ('engineio.server',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\server.py',
   'PYMODULE'),
  ('engineio.socket',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\socket.py',
   'PYMODULE'),
  ('engineio.static_files',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\engineio\\static_files.py',
   'PYMODULE'),
  ('eventlet',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\__init__.py',
   'PYMODULE'),
  ('eventlet.backdoor',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\backdoor.py',
   'PYMODULE'),
  ('eventlet.convenience',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\convenience.py',
   'PYMODULE'),
  ('eventlet.corolocal',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\corolocal.py',
   'PYMODULE'),
  ('eventlet.coros',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\coros.py',
   'PYMODULE'),
  ('eventlet.dagpool',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\dagpool.py',
   'PYMODULE'),
  ('eventlet.db_pool',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\db_pool.py',
   'PYMODULE'),
  ('eventlet.debug',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\debug.py',
   'PYMODULE'),
  ('eventlet.event',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\event.py',
   'PYMODULE'),
  ('eventlet.green',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\__init__.py',
   'PYMODULE'),
  ('eventlet.green.BaseHTTPServer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\BaseHTTPServer.py',
   'PYMODULE'),
  ('eventlet.green.CGIHTTPServer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\CGIHTTPServer.py',
   'PYMODULE'),
  ('eventlet.green.MySQLdb',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\MySQLdb.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL.SSL',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL.crypto',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL.tsafe',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\OpenSSL\\tsafe.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL.version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\OpenSSL\\version.py',
   'PYMODULE'),
  ('eventlet.green.Queue',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\Queue.py',
   'PYMODULE'),
  ('eventlet.green.SimpleHTTPServer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\SimpleHTTPServer.py',
   'PYMODULE'),
  ('eventlet.green.SocketServer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\SocketServer.py',
   'PYMODULE'),
  ('eventlet.green._socket_nodns',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\_socket_nodns.py',
   'PYMODULE'),
  ('eventlet.green.asynchat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\asynchat.py',
   'PYMODULE'),
  ('eventlet.green.asyncore',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\asyncore.py',
   'PYMODULE'),
  ('eventlet.green.builtin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\builtin.py',
   'PYMODULE'),
  ('eventlet.green.ftplib',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\ftplib.py',
   'PYMODULE'),
  ('eventlet.green.http',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\http\\__init__.py',
   'PYMODULE'),
  ('eventlet.green.http.client',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\http\\client.py',
   'PYMODULE'),
  ('eventlet.green.http.cookiejar',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\http\\cookiejar.py',
   'PYMODULE'),
  ('eventlet.green.http.cookies',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\http\\cookies.py',
   'PYMODULE'),
  ('eventlet.green.http.server',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\http\\server.py',
   'PYMODULE'),
  ('eventlet.green.httplib',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\httplib.py',
   'PYMODULE'),
  ('eventlet.green.os',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\os.py',
   'PYMODULE'),
  ('eventlet.green.profile',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\profile.py',
   'PYMODULE'),
  ('eventlet.green.select',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\select.py',
   'PYMODULE'),
  ('eventlet.green.selectors',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\selectors.py',
   'PYMODULE'),
  ('eventlet.green.socket',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\socket.py',
   'PYMODULE'),
  ('eventlet.green.ssl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\ssl.py',
   'PYMODULE'),
  ('eventlet.green.subprocess',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\subprocess.py',
   'PYMODULE'),
  ('eventlet.green.thread',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\thread.py',
   'PYMODULE'),
  ('eventlet.green.threading',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\threading.py',
   'PYMODULE'),
  ('eventlet.green.time',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\time.py',
   'PYMODULE'),
  ('eventlet.green.urllib',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\urllib\\__init__.py',
   'PYMODULE'),
  ('eventlet.green.urllib.error',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\urllib\\error.py',
   'PYMODULE'),
  ('eventlet.green.urllib.parse',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\urllib\\parse.py',
   'PYMODULE'),
  ('eventlet.green.urllib.request',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\urllib\\request.py',
   'PYMODULE'),
  ('eventlet.green.urllib.response',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\urllib\\response.py',
   'PYMODULE'),
  ('eventlet.green.urllib2',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\urllib2.py',
   'PYMODULE'),
  ('eventlet.green.zmq',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\green\\zmq.py',
   'PYMODULE'),
  ('eventlet.greenio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\greenio\\__init__.py',
   'PYMODULE'),
  ('eventlet.greenio.base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\greenio\\base.py',
   'PYMODULE'),
  ('eventlet.greenio.py2',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\greenio\\py2.py',
   'PYMODULE'),
  ('eventlet.greenio.py3',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\greenio\\py3.py',
   'PYMODULE'),
  ('eventlet.greenpool',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\greenpool.py',
   'PYMODULE'),
  ('eventlet.greenthread',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\greenthread.py',
   'PYMODULE'),
  ('eventlet.hubs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\hubs\\__init__.py',
   'PYMODULE'),
  ('eventlet.hubs.epolls',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\hubs\\epolls.py',
   'PYMODULE'),
  ('eventlet.hubs.hub',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\hubs\\hub.py',
   'PYMODULE'),
  ('eventlet.hubs.kqueue',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\hubs\\kqueue.py',
   'PYMODULE'),
  ('eventlet.hubs.poll',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\hubs\\poll.py',
   'PYMODULE'),
  ('eventlet.hubs.pyevent',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\hubs\\pyevent.py',
   'PYMODULE'),
  ('eventlet.hubs.selects',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\hubs\\selects.py',
   'PYMODULE'),
  ('eventlet.hubs.timer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\hubs\\timer.py',
   'PYMODULE'),
  ('eventlet.lock',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\lock.py',
   'PYMODULE'),
  ('eventlet.patcher',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\patcher.py',
   'PYMODULE'),
  ('eventlet.pools',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\pools.py',
   'PYMODULE'),
  ('eventlet.queue',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\queue.py',
   'PYMODULE'),
  ('eventlet.semaphore',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\semaphore.py',
   'PYMODULE'),
  ('eventlet.support',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\support\\__init__.py',
   'PYMODULE'),
  ('eventlet.support.greendns',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\support\\greendns.py',
   'PYMODULE'),
  ('eventlet.support.greenlets',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\support\\greenlets.py',
   'PYMODULE'),
  ('eventlet.support.psycopg2_patcher',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\support\\psycopg2_patcher.py',
   'PYMODULE'),
  ('eventlet.support.pylib',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\support\\pylib.py',
   'PYMODULE'),
  ('eventlet.support.stacklesspypys',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\support\\stacklesspypys.py',
   'PYMODULE'),
  ('eventlet.support.stacklesss',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\support\\stacklesss.py',
   'PYMODULE'),
  ('eventlet.timeout',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\timeout.py',
   'PYMODULE'),
  ('eventlet.tpool',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\tpool.py',
   'PYMODULE'),
  ('eventlet.websocket',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\websocket.py',
   'PYMODULE'),
  ('eventlet.wsgi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\wsgi.py',
   'PYMODULE'),
  ('eventlet.zipkin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\zipkin\\__init__.py',
   'PYMODULE'),
  ('eventlet.zipkin._thrift',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\zipkin\\_thrift\\__init__.py',
   'PYMODULE'),
  ('eventlet.zipkin._thrift.zipkinCore',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\zipkin\\_thrift\\zipkinCore\\__init__.py',
   'PYMODULE'),
  ('eventlet.zipkin._thrift.zipkinCore.constants',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\zipkin\\_thrift\\zipkinCore\\constants.py',
   'PYMODULE'),
  ('eventlet.zipkin._thrift.zipkinCore.ttypes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\zipkin\\_thrift\\zipkinCore\\ttypes.py',
   'PYMODULE'),
  ('eventlet.zipkin.api',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\zipkin\\api.py',
   'PYMODULE'),
  ('eventlet.zipkin.client',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\zipkin\\client.py',
   'PYMODULE'),
  ('eventlet.zipkin.greenthread',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\zipkin\\greenthread.py',
   'PYMODULE'),
  ('eventlet.zipkin.http',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\zipkin\\http.py',
   'PYMODULE'),
  ('eventlet.zipkin.log',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\zipkin\\log.py',
   'PYMODULE'),
  ('eventlet.zipkin.patcher',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\zipkin\\patcher.py',
   'PYMODULE'),
  ('eventlet.zipkin.wsgi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\eventlet\\zipkin\\wsgi.py',
   'PYMODULE'),
  ('exceptiongroup',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\exceptiongroup\\__init__.py',
   'PYMODULE'),
  ('exceptiongroup._catch',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\exceptiongroup\\_catch.py',
   'PYMODULE'),
  ('exceptiongroup._exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\exceptiongroup\\_exceptions.py',
   'PYMODULE'),
  ('exceptiongroup._formatting',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\exceptiongroup\\_formatting.py',
   'PYMODULE'),
  ('exceptiongroup._suppress',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\exceptiongroup\\_suppress.py',
   'PYMODULE'),
  ('exceptiongroup._version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\exceptiongroup\\_version.py',
   'PYMODULE'),
  ('executing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\executing\\__init__.py',
   'PYMODULE'),
  ('executing._exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\executing\\_exceptions.py',
   'PYMODULE'),
  ('executing._position_node_finder',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\executing\\_position_node_finder.py',
   'PYMODULE'),
  ('executing._pytest_utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\executing\\_pytest_utils.py',
   'PYMODULE'),
  ('executing.executing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\executing\\executing.py',
   'PYMODULE'),
  ('executing.version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\executing\\version.py',
   'PYMODULE'),
  ('fastjsonschema',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\fastjsonschema\\__init__.py',
   'PYMODULE'),
  ('fastjsonschema.draft04',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\fastjsonschema\\draft04.py',
   'PYMODULE'),
  ('fastjsonschema.draft06',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\fastjsonschema\\draft06.py',
   'PYMODULE'),
  ('fastjsonschema.draft07',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\fastjsonschema\\draft07.py',
   'PYMODULE'),
  ('fastjsonschema.exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\fastjsonschema\\exceptions.py',
   'PYMODULE'),
  ('fastjsonschema.generator',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\fastjsonschema\\generator.py',
   'PYMODULE'),
  ('fastjsonschema.indent',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\fastjsonschema\\indent.py',
   'PYMODULE'),
  ('fastjsonschema.ref_resolver',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\fastjsonschema\\ref_resolver.py',
   'PYMODULE'),
  ('fastjsonschema.version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\fastjsonschema\\version.py',
   'PYMODULE'),
  ('filecmp',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\filecmp.py',
   'PYMODULE'),
  ('fileinput',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\fileinput.py',
   'PYMODULE'),
  ('flask',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.__main__',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\__main__.py',
   'PYMODULE'),
  ('flask.app',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.blueprints',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.cli',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.config',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.ctx',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.globals',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.helpers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.json',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.json.tag',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('flask.logging',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.scaffold',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\scaffold.py',
   'PYMODULE'),
  ('flask.sessions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.signals',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.templating',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.testing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.typing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.views',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\views.py',
   'PYMODULE'),
  ('flask.wrappers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('flask_socketio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask_socketio\\__init__.py',
   'PYMODULE'),
  ('flask_socketio.namespace',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask_socketio\\namespace.py',
   'PYMODULE'),
  ('flask_socketio.test_client',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\flask_socketio\\test_client.py',
   'PYMODULE'),
  ('fnmatch',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\glob.py',
   'PYMODULE'),
  ('greenlet',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('gzip',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\gzip.py',
   'PYMODULE'),
  ('h11',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\h11\\__init__.py',
   'PYMODULE'),
  ('h11._abnf',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11._connection',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._events',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11._headers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._readers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._state',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('h11._writers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('hashlib',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\http\\server.py',
   'PYMODULE'),
  ('httpcore',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\__init__.py',
   'PYMODULE'),
  ('httpcore._api',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_api.py',
   'PYMODULE'),
  ('httpcore._async',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_async\\__init__.py',
   'PYMODULE'),
  ('httpcore._async.connection',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_async\\connection.py',
   'PYMODULE'),
  ('httpcore._async.connection_pool',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_async\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._async.http11',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_async\\http11.py',
   'PYMODULE'),
  ('httpcore._async.http2',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_async\\http2.py',
   'PYMODULE'),
  ('httpcore._async.http_proxy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_async\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._async.interfaces',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_async\\interfaces.py',
   'PYMODULE'),
  ('httpcore._async.socks_proxy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_async\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._backends',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_backends\\__init__.py',
   'PYMODULE'),
  ('httpcore._backends.anyio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_backends\\anyio.py',
   'PYMODULE'),
  ('httpcore._backends.auto',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_backends\\auto.py',
   'PYMODULE'),
  ('httpcore._backends.base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_backends\\base.py',
   'PYMODULE'),
  ('httpcore._backends.mock',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_backends\\mock.py',
   'PYMODULE'),
  ('httpcore._backends.sync',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_backends\\sync.py',
   'PYMODULE'),
  ('httpcore._backends.trio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_backends\\trio.py',
   'PYMODULE'),
  ('httpcore._exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_exceptions.py',
   'PYMODULE'),
  ('httpcore._models',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_models.py',
   'PYMODULE'),
  ('httpcore._ssl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_ssl.py',
   'PYMODULE'),
  ('httpcore._sync',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_sync\\__init__.py',
   'PYMODULE'),
  ('httpcore._sync.connection',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_sync\\connection.py',
   'PYMODULE'),
  ('httpcore._sync.connection_pool',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_sync\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._sync.http11',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_sync\\http11.py',
   'PYMODULE'),
  ('httpcore._sync.http2',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_sync\\http2.py',
   'PYMODULE'),
  ('httpcore._sync.http_proxy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_sync\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._sync.interfaces',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_sync\\interfaces.py',
   'PYMODULE'),
  ('httpcore._sync.socks_proxy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_sync\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._synchronization',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_synchronization.py',
   'PYMODULE'),
  ('httpcore._trace',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_trace.py',
   'PYMODULE'),
  ('httpcore._utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpcore\\_utils.py',
   'PYMODULE'),
  ('httpx',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\__init__.py',
   'PYMODULE'),
  ('httpx.__version__',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\__version__.py',
   'PYMODULE'),
  ('httpx._api',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_api.py',
   'PYMODULE'),
  ('httpx._auth',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_auth.py',
   'PYMODULE'),
  ('httpx._client',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_client.py',
   'PYMODULE'),
  ('httpx._compat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_compat.py',
   'PYMODULE'),
  ('httpx._config',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_config.py',
   'PYMODULE'),
  ('httpx._content',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_content.py',
   'PYMODULE'),
  ('httpx._decoders',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_decoders.py',
   'PYMODULE'),
  ('httpx._exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_exceptions.py',
   'PYMODULE'),
  ('httpx._main',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_main.py',
   'PYMODULE'),
  ('httpx._models',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_models.py',
   'PYMODULE'),
  ('httpx._multipart',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_multipart.py',
   'PYMODULE'),
  ('httpx._status_codes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_status_codes.py',
   'PYMODULE'),
  ('httpx._transports',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_transports\\__init__.py',
   'PYMODULE'),
  ('httpx._transports.asgi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_transports\\asgi.py',
   'PYMODULE'),
  ('httpx._transports.base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_transports\\base.py',
   'PYMODULE'),
  ('httpx._transports.default',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_transports\\default.py',
   'PYMODULE'),
  ('httpx._transports.mock',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_transports\\mock.py',
   'PYMODULE'),
  ('httpx._transports.wsgi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_transports\\wsgi.py',
   'PYMODULE'),
  ('httpx._types',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_types.py',
   'PYMODULE'),
  ('httpx._urlparse',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_urlparse.py',
   'PYMODULE'),
  ('httpx._urls',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_urls.py',
   'PYMODULE'),
  ('httpx._utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\httpx\\_utils.py',
   'PYMODULE'),
  ('idna',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('imp',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\imp.py',
   'PYMODULE'),
  ('importlib',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._adapters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\importlib\\_adapters.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib.abc',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib_metadata',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('inspect',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\ipaddress.py',
   'PYMODULE'),
  ('ipykernel',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\__init__.py',
   'PYMODULE'),
  ('ipykernel._eventloop_macos',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\_eventloop_macos.py',
   'PYMODULE'),
  ('ipykernel._version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\_version.py',
   'PYMODULE'),
  ('ipykernel.comm',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\comm\\__init__.py',
   'PYMODULE'),
  ('ipykernel.comm.comm',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\comm\\comm.py',
   'PYMODULE'),
  ('ipykernel.comm.manager',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\comm\\manager.py',
   'PYMODULE'),
  ('ipykernel.compiler',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\compiler.py',
   'PYMODULE'),
  ('ipykernel.connect',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\connect.py',
   'PYMODULE'),
  ('ipykernel.control',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\control.py',
   'PYMODULE'),
  ('ipykernel.debugger',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\debugger.py',
   'PYMODULE'),
  ('ipykernel.displayhook',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\displayhook.py',
   'PYMODULE'),
  ('ipykernel.embed',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\embed.py',
   'PYMODULE'),
  ('ipykernel.eventloops',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\eventloops.py',
   'PYMODULE'),
  ('ipykernel.gui',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\gui\\__init__.py',
   'PYMODULE'),
  ('ipykernel.gui.gtk3embed',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\gui\\gtk3embed.py',
   'PYMODULE'),
  ('ipykernel.gui.gtkembed',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\gui\\gtkembed.py',
   'PYMODULE'),
  ('ipykernel.heartbeat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\heartbeat.py',
   'PYMODULE'),
  ('ipykernel.iostream',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\iostream.py',
   'PYMODULE'),
  ('ipykernel.ipkernel',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\ipkernel.py',
   'PYMODULE'),
  ('ipykernel.jsonutil',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\jsonutil.py',
   'PYMODULE'),
  ('ipykernel.kernelapp',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\kernelapp.py',
   'PYMODULE'),
  ('ipykernel.kernelbase',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\kernelbase.py',
   'PYMODULE'),
  ('ipykernel.kernelspec',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\kernelspec.py',
   'PYMODULE'),
  ('ipykernel.parentpoller',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\parentpoller.py',
   'PYMODULE'),
  ('ipykernel.pickleutil',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\pickleutil.py',
   'PYMODULE'),
  ('ipykernel.serialize',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\serialize.py',
   'PYMODULE'),
  ('ipykernel.trio_runner',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\trio_runner.py',
   'PYMODULE'),
  ('ipykernel.zmqshell',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\ipykernel\\zmqshell.py',
   'PYMODULE'),
  ('itsdangerous',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jaraco.collections',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\collections\\__init__.py',
   'PYMODULE'),
  ('jedi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\__init__.py',
   'PYMODULE'),
  ('jedi._compatibility',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\_compatibility.py',
   'PYMODULE'),
  ('jedi.api',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\api\\__init__.py',
   'PYMODULE'),
  ('jedi.api.classes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\api\\classes.py',
   'PYMODULE'),
  ('jedi.api.completion',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\api\\completion.py',
   'PYMODULE'),
  ('jedi.api.completion_cache',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\api\\completion_cache.py',
   'PYMODULE'),
  ('jedi.api.environment',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\api\\environment.py',
   'PYMODULE'),
  ('jedi.api.errors',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\api\\errors.py',
   'PYMODULE'),
  ('jedi.api.exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\api\\exceptions.py',
   'PYMODULE'),
  ('jedi.api.file_name',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\api\\file_name.py',
   'PYMODULE'),
  ('jedi.api.helpers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\api\\helpers.py',
   'PYMODULE'),
  ('jedi.api.interpreter',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\api\\interpreter.py',
   'PYMODULE'),
  ('jedi.api.keywords',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\api\\keywords.py',
   'PYMODULE'),
  ('jedi.api.project',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\api\\project.py',
   'PYMODULE'),
  ('jedi.api.refactoring',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\api\\refactoring\\__init__.py',
   'PYMODULE'),
  ('jedi.api.refactoring.extract',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\api\\refactoring\\extract.py',
   'PYMODULE'),
  ('jedi.api.strings',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\api\\strings.py',
   'PYMODULE'),
  ('jedi.cache',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\cache.py',
   'PYMODULE'),
  ('jedi.common',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\common.py',
   'PYMODULE'),
  ('jedi.debug',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\debug.py',
   'PYMODULE'),
  ('jedi.file_io',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\file_io.py',
   'PYMODULE'),
  ('jedi.inference',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.analysis',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\analysis.py',
   'PYMODULE'),
  ('jedi.inference.arguments',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\arguments.py',
   'PYMODULE'),
  ('jedi.inference.base_value',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\base_value.py',
   'PYMODULE'),
  ('jedi.inference.cache',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\cache.py',
   'PYMODULE'),
  ('jedi.inference.compiled',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\compiled\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.compiled.access',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\compiled\\access.py',
   'PYMODULE'),
  ('jedi.inference.compiled.getattr_static',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\compiled\\getattr_static.py',
   'PYMODULE'),
  ('jedi.inference.compiled.mixed',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\compiled\\mixed.py',
   'PYMODULE'),
  ('jedi.inference.compiled.subprocess',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\compiled\\subprocess\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.compiled.subprocess.functions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\compiled\\subprocess\\functions.py',
   'PYMODULE'),
  ('jedi.inference.compiled.value',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\compiled\\value.py',
   'PYMODULE'),
  ('jedi.inference.context',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\context.py',
   'PYMODULE'),
  ('jedi.inference.docstring_utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\docstring_utils.py',
   'PYMODULE'),
  ('jedi.inference.docstrings',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\docstrings.py',
   'PYMODULE'),
  ('jedi.inference.dynamic_params',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\dynamic_params.py',
   'PYMODULE'),
  ('jedi.inference.filters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\filters.py',
   'PYMODULE'),
  ('jedi.inference.finder',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\finder.py',
   'PYMODULE'),
  ('jedi.inference.flow_analysis',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\flow_analysis.py',
   'PYMODULE'),
  ('jedi.inference.gradual',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\gradual\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.gradual.annotation',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\gradual\\annotation.py',
   'PYMODULE'),
  ('jedi.inference.gradual.base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\gradual\\base.py',
   'PYMODULE'),
  ('jedi.inference.gradual.conversion',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\gradual\\conversion.py',
   'PYMODULE'),
  ('jedi.inference.gradual.generics',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\gradual\\generics.py',
   'PYMODULE'),
  ('jedi.inference.gradual.stub_value',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\gradual\\stub_value.py',
   'PYMODULE'),
  ('jedi.inference.gradual.type_var',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\gradual\\type_var.py',
   'PYMODULE'),
  ('jedi.inference.gradual.typeshed',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\gradual\\typeshed.py',
   'PYMODULE'),
  ('jedi.inference.gradual.typing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\gradual\\typing.py',
   'PYMODULE'),
  ('jedi.inference.gradual.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\gradual\\utils.py',
   'PYMODULE'),
  ('jedi.inference.helpers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\helpers.py',
   'PYMODULE'),
  ('jedi.inference.imports',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\imports.py',
   'PYMODULE'),
  ('jedi.inference.lazy_value',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\lazy_value.py',
   'PYMODULE'),
  ('jedi.inference.names',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\names.py',
   'PYMODULE'),
  ('jedi.inference.param',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\param.py',
   'PYMODULE'),
  ('jedi.inference.parser_cache',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\parser_cache.py',
   'PYMODULE'),
  ('jedi.inference.recursion',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\recursion.py',
   'PYMODULE'),
  ('jedi.inference.references',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\references.py',
   'PYMODULE'),
  ('jedi.inference.signature',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\signature.py',
   'PYMODULE'),
  ('jedi.inference.star_args',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\star_args.py',
   'PYMODULE'),
  ('jedi.inference.syntax_tree',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\syntax_tree.py',
   'PYMODULE'),
  ('jedi.inference.sys_path',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\sys_path.py',
   'PYMODULE'),
  ('jedi.inference.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\utils.py',
   'PYMODULE'),
  ('jedi.inference.value',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\value\\__init__.py',
   'PYMODULE'),
  ('jedi.inference.value.decorator',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\value\\decorator.py',
   'PYMODULE'),
  ('jedi.inference.value.dynamic_arrays',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\value\\dynamic_arrays.py',
   'PYMODULE'),
  ('jedi.inference.value.function',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\value\\function.py',
   'PYMODULE'),
  ('jedi.inference.value.instance',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\value\\instance.py',
   'PYMODULE'),
  ('jedi.inference.value.iterable',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\value\\iterable.py',
   'PYMODULE'),
  ('jedi.inference.value.klass',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\value\\klass.py',
   'PYMODULE'),
  ('jedi.inference.value.module',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\value\\module.py',
   'PYMODULE'),
  ('jedi.inference.value.namespace',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\inference\\value\\namespace.py',
   'PYMODULE'),
  ('jedi.parser_utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\parser_utils.py',
   'PYMODULE'),
  ('jedi.plugins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\plugins\\__init__.py',
   'PYMODULE'),
  ('jedi.plugins.django',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\plugins\\django.py',
   'PYMODULE'),
  ('jedi.plugins.flask',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\plugins\\flask.py',
   'PYMODULE'),
  ('jedi.plugins.pytest',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\plugins\\pytest.py',
   'PYMODULE'),
  ('jedi.plugins.registry',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\plugins\\registry.py',
   'PYMODULE'),
  ('jedi.plugins.stdlib',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\plugins\\stdlib.py',
   'PYMODULE'),
  ('jedi.settings',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jedi\\settings.py',
   'PYMODULE'),
  ('jinja2',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('jsonschema',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jsonschema\\__init__.py',
   'PYMODULE'),
  ('jsonschema._format',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jsonschema\\_format.py',
   'PYMODULE'),
  ('jsonschema._keywords',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jsonschema\\_keywords.py',
   'PYMODULE'),
  ('jsonschema._legacy_keywords',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jsonschema\\_legacy_keywords.py',
   'PYMODULE'),
  ('jsonschema._types',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jsonschema\\_types.py',
   'PYMODULE'),
  ('jsonschema._typing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jsonschema\\_typing.py',
   'PYMODULE'),
  ('jsonschema._utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jsonschema\\_utils.py',
   'PYMODULE'),
  ('jsonschema.exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jsonschema\\exceptions.py',
   'PYMODULE'),
  ('jsonschema.protocols',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jsonschema\\protocols.py',
   'PYMODULE'),
  ('jsonschema.validators',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jsonschema\\validators.py',
   'PYMODULE'),
  ('jsonschema_specifications',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jsonschema_specifications\\__init__.py',
   'PYMODULE'),
  ('jsonschema_specifications._core',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jsonschema_specifications\\_core.py',
   'PYMODULE'),
  ('jupyter_client',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\__init__.py',
   'PYMODULE'),
  ('jupyter_client._version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\_version.py',
   'PYMODULE'),
  ('jupyter_client.adapter',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\adapter.py',
   'PYMODULE'),
  ('jupyter_client.asynchronous',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\asynchronous\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.asynchronous.client',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\asynchronous\\client.py',
   'PYMODULE'),
  ('jupyter_client.blocking',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\blocking\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.blocking.client',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\blocking\\client.py',
   'PYMODULE'),
  ('jupyter_client.channels',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\channels.py',
   'PYMODULE'),
  ('jupyter_client.channelsabc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\channelsabc.py',
   'PYMODULE'),
  ('jupyter_client.client',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\client.py',
   'PYMODULE'),
  ('jupyter_client.clientabc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\clientabc.py',
   'PYMODULE'),
  ('jupyter_client.connect',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\connect.py',
   'PYMODULE'),
  ('jupyter_client.jsonutil',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\jsonutil.py',
   'PYMODULE'),
  ('jupyter_client.kernelspec',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\kernelspec.py',
   'PYMODULE'),
  ('jupyter_client.launcher',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\launcher.py',
   'PYMODULE'),
  ('jupyter_client.localinterfaces',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\localinterfaces.py',
   'PYMODULE'),
  ('jupyter_client.manager',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\manager.py',
   'PYMODULE'),
  ('jupyter_client.managerabc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\managerabc.py',
   'PYMODULE'),
  ('jupyter_client.multikernelmanager',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\multikernelmanager.py',
   'PYMODULE'),
  ('jupyter_client.provisioning',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\provisioning\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.factory',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\provisioning\\factory.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.local_provisioner',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\provisioning\\local_provisioner.py',
   'PYMODULE'),
  ('jupyter_client.provisioning.provisioner_base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\provisioning\\provisioner_base.py',
   'PYMODULE'),
  ('jupyter_client.session',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\session.py',
   'PYMODULE'),
  ('jupyter_client.ssh',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\ssh\\__init__.py',
   'PYMODULE'),
  ('jupyter_client.ssh.forward',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\ssh\\forward.py',
   'PYMODULE'),
  ('jupyter_client.ssh.tunnel',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\ssh\\tunnel.py',
   'PYMODULE'),
  ('jupyter_client.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\utils.py',
   'PYMODULE'),
  ('jupyter_client.win_interrupt',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_client\\win_interrupt.py',
   'PYMODULE'),
  ('jupyter_core',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_core\\__init__.py',
   'PYMODULE'),
  ('jupyter_core.paths',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_core\\paths.py',
   'PYMODULE'),
  ('jupyter_core.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_core\\utils\\__init__.py',
   'PYMODULE'),
  ('jupyter_core.version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\jupyter_core\\version.py',
   'PYMODULE'),
  ('logging',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.config',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\logging\\config.py',
   'PYMODULE'),
  ('logging.handlers',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lzma',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\lzma.py',
   'PYMODULE'),
  ('markdown_it',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\__init__.py',
   'PYMODULE'),
  ('markdown_it._compat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\_compat.py',
   'PYMODULE'),
  ('markdown_it._punycode',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\_punycode.py',
   'PYMODULE'),
  ('markdown_it.common',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\common\\__init__.py',
   'PYMODULE'),
  ('markdown_it.common.entities',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\common\\entities.py',
   'PYMODULE'),
  ('markdown_it.common.html_blocks',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\common\\html_blocks.py',
   'PYMODULE'),
  ('markdown_it.common.html_re',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\common\\html_re.py',
   'PYMODULE'),
  ('markdown_it.common.normalize_url',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\common\\normalize_url.py',
   'PYMODULE'),
  ('markdown_it.common.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\common\\utils.py',
   'PYMODULE'),
  ('markdown_it.helpers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\helpers\\__init__.py',
   'PYMODULE'),
  ('markdown_it.helpers.parse_link_destination',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\helpers\\parse_link_destination.py',
   'PYMODULE'),
  ('markdown_it.helpers.parse_link_label',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\helpers\\parse_link_label.py',
   'PYMODULE'),
  ('markdown_it.helpers.parse_link_title',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\helpers\\parse_link_title.py',
   'PYMODULE'),
  ('markdown_it.main',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\main.py',
   'PYMODULE'),
  ('markdown_it.parser_block',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\parser_block.py',
   'PYMODULE'),
  ('markdown_it.parser_core',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\parser_core.py',
   'PYMODULE'),
  ('markdown_it.parser_inline',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\parser_inline.py',
   'PYMODULE'),
  ('markdown_it.presets',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\presets\\__init__.py',
   'PYMODULE'),
  ('markdown_it.presets.commonmark',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\presets\\commonmark.py',
   'PYMODULE'),
  ('markdown_it.presets.default',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\presets\\default.py',
   'PYMODULE'),
  ('markdown_it.presets.zero',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\presets\\zero.py',
   'PYMODULE'),
  ('markdown_it.renderer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\renderer.py',
   'PYMODULE'),
  ('markdown_it.ruler',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\ruler.py',
   'PYMODULE'),
  ('markdown_it.rules_block',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_block\\__init__.py',
   'PYMODULE'),
  ('markdown_it.rules_block.blockquote',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_block\\blockquote.py',
   'PYMODULE'),
  ('markdown_it.rules_block.code',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_block\\code.py',
   'PYMODULE'),
  ('markdown_it.rules_block.fence',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_block\\fence.py',
   'PYMODULE'),
  ('markdown_it.rules_block.heading',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_block\\heading.py',
   'PYMODULE'),
  ('markdown_it.rules_block.hr',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_block\\hr.py',
   'PYMODULE'),
  ('markdown_it.rules_block.html_block',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_block\\html_block.py',
   'PYMODULE'),
  ('markdown_it.rules_block.lheading',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_block\\lheading.py',
   'PYMODULE'),
  ('markdown_it.rules_block.list',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_block\\list.py',
   'PYMODULE'),
  ('markdown_it.rules_block.paragraph',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_block\\paragraph.py',
   'PYMODULE'),
  ('markdown_it.rules_block.reference',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_block\\reference.py',
   'PYMODULE'),
  ('markdown_it.rules_block.state_block',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_block\\state_block.py',
   'PYMODULE'),
  ('markdown_it.rules_block.table',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_block\\table.py',
   'PYMODULE'),
  ('markdown_it.rules_core',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_core\\__init__.py',
   'PYMODULE'),
  ('markdown_it.rules_core.block',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_core\\block.py',
   'PYMODULE'),
  ('markdown_it.rules_core.inline',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_core\\inline.py',
   'PYMODULE'),
  ('markdown_it.rules_core.linkify',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_core\\linkify.py',
   'PYMODULE'),
  ('markdown_it.rules_core.normalize',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_core\\normalize.py',
   'PYMODULE'),
  ('markdown_it.rules_core.replacements',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_core\\replacements.py',
   'PYMODULE'),
  ('markdown_it.rules_core.smartquotes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_core\\smartquotes.py',
   'PYMODULE'),
  ('markdown_it.rules_core.state_core',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_core\\state_core.py',
   'PYMODULE'),
  ('markdown_it.rules_core.text_join',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_core\\text_join.py',
   'PYMODULE'),
  ('markdown_it.rules_inline',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_inline\\__init__.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.autolink',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_inline\\autolink.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.backticks',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_inline\\backticks.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.balance_pairs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_inline\\balance_pairs.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.emphasis',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_inline\\emphasis.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.entity',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_inline\\entity.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.escape',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_inline\\escape.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.fragments_join',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_inline\\fragments_join.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.html_inline',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_inline\\html_inline.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.image',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_inline\\image.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.link',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_inline\\link.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.linkify',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_inline\\linkify.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.newline',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_inline\\newline.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.state_inline',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_inline\\state_inline.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.strikethrough',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_inline\\strikethrough.py',
   'PYMODULE'),
  ('markdown_it.rules_inline.text',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\rules_inline\\text.py',
   'PYMODULE'),
  ('markdown_it.token',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\token.py',
   'PYMODULE'),
  ('markdown_it.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markdown_it\\utils.py',
   'PYMODULE'),
  ('markupsafe',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mdurl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\mdurl\\__init__.py',
   'PYMODULE'),
  ('mdurl._decode',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\mdurl\\_decode.py',
   'PYMODULE'),
  ('mdurl._encode',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\mdurl\\_encode.py',
   'PYMODULE'),
  ('mdurl._format',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\mdurl\\_format.py',
   'PYMODULE'),
  ('mdurl._parse',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\mdurl\\_parse.py',
   'PYMODULE'),
  ('mdurl._url',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\mdurl\\_url.py',
   'PYMODULE'),
  ('mimetypes',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\mimetypes.py',
   'PYMODULE'),
  ('models', 'D:\\testcode\\typegame改\\server\\models.py', 'PYMODULE'),
  ('multiprocessing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('nbformat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\__init__.py',
   'PYMODULE'),
  ('nbformat._imports',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\_imports.py',
   'PYMODULE'),
  ('nbformat._struct',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\_struct.py',
   'PYMODULE'),
  ('nbformat._version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\_version.py',
   'PYMODULE'),
  ('nbformat.converter',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\converter.py',
   'PYMODULE'),
  ('nbformat.corpus',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\corpus\\__init__.py',
   'PYMODULE'),
  ('nbformat.corpus.words',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\corpus\\words.py',
   'PYMODULE'),
  ('nbformat.json_compat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\json_compat.py',
   'PYMODULE'),
  ('nbformat.notebooknode',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\notebooknode.py',
   'PYMODULE'),
  ('nbformat.reader',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\reader.py',
   'PYMODULE'),
  ('nbformat.sentinel',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\sentinel.py',
   'PYMODULE'),
  ('nbformat.v1',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v1\\__init__.py',
   'PYMODULE'),
  ('nbformat.v1.convert',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v1\\convert.py',
   'PYMODULE'),
  ('nbformat.v1.nbbase',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v1\\nbbase.py',
   'PYMODULE'),
  ('nbformat.v1.nbjson',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v1\\nbjson.py',
   'PYMODULE'),
  ('nbformat.v1.rwbase',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v1\\rwbase.py',
   'PYMODULE'),
  ('nbformat.v2',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v2\\__init__.py',
   'PYMODULE'),
  ('nbformat.v2.convert',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v2\\convert.py',
   'PYMODULE'),
  ('nbformat.v2.nbbase',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v2\\nbbase.py',
   'PYMODULE'),
  ('nbformat.v2.nbjson',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v2\\nbjson.py',
   'PYMODULE'),
  ('nbformat.v2.nbpy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v2\\nbpy.py',
   'PYMODULE'),
  ('nbformat.v2.nbxml',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v2\\nbxml.py',
   'PYMODULE'),
  ('nbformat.v2.rwbase',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v2\\rwbase.py',
   'PYMODULE'),
  ('nbformat.v3',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v3\\__init__.py',
   'PYMODULE'),
  ('nbformat.v3.convert',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v3\\convert.py',
   'PYMODULE'),
  ('nbformat.v3.nbbase',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v3\\nbbase.py',
   'PYMODULE'),
  ('nbformat.v3.nbjson',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v3\\nbjson.py',
   'PYMODULE'),
  ('nbformat.v3.nbpy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v3\\nbpy.py',
   'PYMODULE'),
  ('nbformat.v3.rwbase',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v3\\rwbase.py',
   'PYMODULE'),
  ('nbformat.v4',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v4\\__init__.py',
   'PYMODULE'),
  ('nbformat.v4.convert',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v4\\convert.py',
   'PYMODULE'),
  ('nbformat.v4.nbbase',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v4\\nbbase.py',
   'PYMODULE'),
  ('nbformat.v4.nbjson',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v4\\nbjson.py',
   'PYMODULE'),
  ('nbformat.v4.rwbase',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\v4\\rwbase.py',
   'PYMODULE'),
  ('nbformat.validator',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\validator.py',
   'PYMODULE'),
  ('nbformat.warnings',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nbformat\\warnings.py',
   'PYMODULE'),
  ('nest_asyncio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\nest_asyncio.py',
   'PYMODULE'),
  ('netrc',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\netrc.py',
   'PYMODULE'),
  ('ntsecuritycon',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\win32\\lib\\ntsecuritycon.py',
   'PYMODULE'),
  ('nturl2path',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\opcode.py',
   'PYMODULE'),
  ('optparse',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\optparse.py',
   'PYMODULE'),
  ('packaging',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('parso',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\__init__.py',
   'PYMODULE'),
  ('parso._compatibility',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\_compatibility.py',
   'PYMODULE'),
  ('parso.cache',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\cache.py',
   'PYMODULE'),
  ('parso.file_io',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\file_io.py',
   'PYMODULE'),
  ('parso.grammar',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\grammar.py',
   'PYMODULE'),
  ('parso.normalizer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\normalizer.py',
   'PYMODULE'),
  ('parso.parser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\parser.py',
   'PYMODULE'),
  ('parso.pgen2',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\pgen2\\__init__.py',
   'PYMODULE'),
  ('parso.pgen2.generator',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\pgen2\\generator.py',
   'PYMODULE'),
  ('parso.pgen2.grammar_parser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\pgen2\\grammar_parser.py',
   'PYMODULE'),
  ('parso.python',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\python\\__init__.py',
   'PYMODULE'),
  ('parso.python.diff',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\python\\diff.py',
   'PYMODULE'),
  ('parso.python.errors',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\python\\errors.py',
   'PYMODULE'),
  ('parso.python.parser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\python\\parser.py',
   'PYMODULE'),
  ('parso.python.pep8',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\python\\pep8.py',
   'PYMODULE'),
  ('parso.python.prefix',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\python\\prefix.py',
   'PYMODULE'),
  ('parso.python.token',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\python\\token.py',
   'PYMODULE'),
  ('parso.python.tokenize',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\python\\tokenize.py',
   'PYMODULE'),
  ('parso.python.tree',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\python\\tree.py',
   'PYMODULE'),
  ('parso.tree',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\tree.py',
   'PYMODULE'),
  ('parso.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\parso\\utils.py',
   'PYMODULE'),
  ('pathlib',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\pathlib.py',
   'PYMODULE'),
  ('pdb',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\pickle.py',
   'PYMODULE'),
  ('pkg_resources',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\platform.py',
   'PYMODULE'),
  ('platformdirs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\platformdirs\\android.py',
   'PYMODULE'),
  ('platformdirs.api',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\platformdirs\\api.py',
   'PYMODULE'),
  ('platformdirs.macos',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\platformdirs\\macos.py',
   'PYMODULE'),
  ('platformdirs.unix',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\platformdirs\\unix.py',
   'PYMODULE'),
  ('platformdirs.version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\platformdirs\\windows.py',
   'PYMODULE'),
  ('plistlib',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\plistlib.py',
   'PYMODULE'),
  ('pprint',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\pprint.py',
   'PYMODULE'),
  ('profile',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\profile.py',
   'PYMODULE'),
  ('prompt_toolkit',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.application',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\application\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.application.application',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\application\\application.py',
   'PYMODULE'),
  ('prompt_toolkit.application.current',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\application\\current.py',
   'PYMODULE'),
  ('prompt_toolkit.application.dummy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\application\\dummy.py',
   'PYMODULE'),
  ('prompt_toolkit.application.run_in_terminal',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\application\\run_in_terminal.py',
   'PYMODULE'),
  ('prompt_toolkit.auto_suggest',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\auto_suggest.py',
   'PYMODULE'),
  ('prompt_toolkit.buffer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\buffer.py',
   'PYMODULE'),
  ('prompt_toolkit.cache',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\cache.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\clipboard\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard.base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\clipboard\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard.in_memory',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\clipboard\\in_memory.py',
   'PYMODULE'),
  ('prompt_toolkit.completion',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\completion\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\completion\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.deduplicate',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\completion\\deduplicate.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.filesystem',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\completion\\filesystem.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.fuzzy_completer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\completion\\fuzzy_completer.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.nested',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\completion\\nested.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.word_completer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\completion\\word_completer.py',
   'PYMODULE'),
  ('prompt_toolkit.cursor_shapes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\cursor_shapes.py',
   'PYMODULE'),
  ('prompt_toolkit.data_structures',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\data_structures.py',
   'PYMODULE'),
  ('prompt_toolkit.document',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\document.py',
   'PYMODULE'),
  ('prompt_toolkit.enums',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\enums.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\eventloop\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.async_generator',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\eventloop\\async_generator.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.inputhook',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\eventloop\\inputhook.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\eventloop\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.win32',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\eventloop\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.filters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\filters\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.app',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\filters\\app.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\filters\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.cli',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\filters\\cli.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\filters\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\formatted_text\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.ansi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\formatted_text\\ansi.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\formatted_text\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.html',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\formatted_text\\html.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.pygments',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\formatted_text\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\formatted_text\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.history',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\history.py',
   'PYMODULE'),
  ('prompt_toolkit.input',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\input\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.input.ansi_escape_sequences',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\input\\ansi_escape_sequences.py',
   'PYMODULE'),
  ('prompt_toolkit.input.base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\input\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.input.defaults',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\input\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.input.posix_pipe',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\input\\posix_pipe.py',
   'PYMODULE'),
  ('prompt_toolkit.input.posix_utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\input\\posix_utils.py',
   'PYMODULE'),
  ('prompt_toolkit.input.typeahead',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\input\\typeahead.py',
   'PYMODULE'),
  ('prompt_toolkit.input.vt100',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\input\\vt100.py',
   'PYMODULE'),
  ('prompt_toolkit.input.vt100_parser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\input\\vt100_parser.py',
   'PYMODULE'),
  ('prompt_toolkit.input.win32',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\input\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.input.win32_pipe',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\input\\win32_pipe.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.auto_suggest',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\auto_suggest.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.basic',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\basic.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.completion',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\completion.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.cpr',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\cpr.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.emacs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\emacs.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.focus',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\focus.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.mouse',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\mouse.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.named_commands',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\named_commands.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.open_in_editor',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\open_in_editor.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.page_navigation',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\page_navigation.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.scroll',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\scroll.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.search',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\search.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.vi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\vi.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.defaults',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.digraphs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\digraphs.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.emacs_state',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\emacs_state.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.key_bindings',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\key_bindings.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.key_processor',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\key_processor.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.vi_state',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\key_binding\\vi_state.py',
   'PYMODULE'),
  ('prompt_toolkit.keys',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\keys.py',
   'PYMODULE'),
  ('prompt_toolkit.layout',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\layout\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.containers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\layout\\containers.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.controls',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\layout\\controls.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.dimension',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\layout\\dimension.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.dummy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\layout\\dummy.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.layout',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\layout\\layout.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.margins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\layout\\margins.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.menus',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\layout\\menus.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.mouse_handlers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\layout\\mouse_handlers.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.processors',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\layout\\processors.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.screen',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\layout\\screen.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.scrollable_pane',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\layout\\scrollable_pane.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\layout\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\lexers\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers.base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\lexers\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers.pygments',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\lexers\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.mouse_events',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\mouse_events.py',
   'PYMODULE'),
  ('prompt_toolkit.output',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\output\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.output.base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\output\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.output.color_depth',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\output\\color_depth.py',
   'PYMODULE'),
  ('prompt_toolkit.output.conemu',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\output\\conemu.py',
   'PYMODULE'),
  ('prompt_toolkit.output.defaults',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\output\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.output.flush_stdout',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\output\\flush_stdout.py',
   'PYMODULE'),
  ('prompt_toolkit.output.plain_text',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\output\\plain_text.py',
   'PYMODULE'),
  ('prompt_toolkit.output.vt100',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\output\\vt100.py',
   'PYMODULE'),
  ('prompt_toolkit.output.win32',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\output\\win32.py',
   'PYMODULE'),
  ('prompt_toolkit.output.windows10',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\output\\windows10.py',
   'PYMODULE'),
  ('prompt_toolkit.patch_stdout',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\patch_stdout.py',
   'PYMODULE'),
  ('prompt_toolkit.renderer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\renderer.py',
   'PYMODULE'),
  ('prompt_toolkit.search',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\search.py',
   'PYMODULE'),
  ('prompt_toolkit.selection',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\selection.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\shortcuts\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.dialogs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\shortcuts\\dialogs.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar.base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar.formatters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\shortcuts\\progress_bar\\formatters.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.prompt',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\shortcuts\\prompt.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\shortcuts\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.styles',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\styles\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\styles\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.defaults',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\styles\\defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.named_colors',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\styles\\named_colors.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.pygments',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\styles\\pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.style',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\styles\\style.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.style_transformation',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\styles\\style_transformation.py',
   'PYMODULE'),
  ('prompt_toolkit.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\utils.py',
   'PYMODULE'),
  ('prompt_toolkit.validation',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\validation.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\widgets\\__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\widgets\\base.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.dialogs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\widgets\\dialogs.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.menus',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\widgets\\menus.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.toolbars',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\widgets\\toolbars.py',
   'PYMODULE'),
  ('prompt_toolkit.win32_types',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\prompt_toolkit\\win32_types.py',
   'PYMODULE'),
  ('pstats',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\pstats.py',
   'PYMODULE'),
  ('psutil',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('pty',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\pty.py',
   'PYMODULE'),
  ('pure_eval',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pure_eval\\__init__.py',
   'PYMODULE'),
  ('pure_eval.core',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pure_eval\\core.py',
   'PYMODULE'),
  ('pure_eval.my_getattr_static',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pure_eval\\my_getattr_static.py',
   'PYMODULE'),
  ('pure_eval.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pure_eval\\utils.py',
   'PYMODULE'),
  ('pure_eval.version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pure_eval\\version.py',
   'PYMODULE'),
  ('py_compile',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\py_compile.py',
   'PYMODULE'),
  ('pycparser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pygments',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\__init__.py',
   'PYMODULE'),
  ('pygments.console',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\console.py',
   'PYMODULE'),
  ('pygments.filter',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\filter.py',
   'PYMODULE'),
  ('pygments.filters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\filters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatter',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\formatter.py',
   'PYMODULE'),
  ('pygments.formatters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\formatters\\__init__.py',
   'PYMODULE'),
  ('pygments.formatters._mapping',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\formatters\\_mapping.py',
   'PYMODULE'),
  ('pygments.formatters.bbcode',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\formatters\\bbcode.py',
   'PYMODULE'),
  ('pygments.formatters.groff',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\formatters\\groff.py',
   'PYMODULE'),
  ('pygments.formatters.html',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\formatters\\html.py',
   'PYMODULE'),
  ('pygments.formatters.img',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\formatters\\img.py',
   'PYMODULE'),
  ('pygments.formatters.irc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\formatters\\irc.py',
   'PYMODULE'),
  ('pygments.formatters.latex',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\formatters\\latex.py',
   'PYMODULE'),
  ('pygments.formatters.other',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\formatters\\other.py',
   'PYMODULE'),
  ('pygments.formatters.pangomarkup',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\formatters\\pangomarkup.py',
   'PYMODULE'),
  ('pygments.formatters.rtf',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\formatters\\rtf.py',
   'PYMODULE'),
  ('pygments.formatters.svg',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\formatters\\svg.py',
   'PYMODULE'),
  ('pygments.formatters.terminal',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\formatters\\terminal.py',
   'PYMODULE'),
  ('pygments.formatters.terminal256',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\formatters\\terminal256.py',
   'PYMODULE'),
  ('pygments.lexer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexer.py',
   'PYMODULE'),
  ('pygments.lexers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\__init__.py',
   'PYMODULE'),
  ('pygments.lexers._ada_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_ada_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._asy_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_asy_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cl_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_cl_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._cocoa_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_cocoa_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._csound_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_csound_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._css_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_css_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._googlesql_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_googlesql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._julia_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_julia_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lasso_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_lasso_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lilypond_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_lilypond_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._lua_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_lua_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._luau_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_luau_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mapping',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_mapping.py',
   'PYMODULE'),
  ('pygments.lexers._mql_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_mql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._mysql_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_mysql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._openedge_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_openedge_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._php_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_php_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._postgres_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_postgres_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._qlik_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_qlik_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scheme_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_scheme_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._scilab_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_scilab_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._sourcemod_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_sourcemod_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stan_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_stan_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._stata_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_stata_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._tsql_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_tsql_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._usd_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_usd_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vbscript_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_vbscript_builtins.py',
   'PYMODULE'),
  ('pygments.lexers._vim_builtins',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\_vim_builtins.py',
   'PYMODULE'),
  ('pygments.lexers.actionscript',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\actionscript.py',
   'PYMODULE'),
  ('pygments.lexers.ada',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\ada.py',
   'PYMODULE'),
  ('pygments.lexers.agile',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\agile.py',
   'PYMODULE'),
  ('pygments.lexers.algebra',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\algebra.py',
   'PYMODULE'),
  ('pygments.lexers.ambient',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\ambient.py',
   'PYMODULE'),
  ('pygments.lexers.amdgpu',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\amdgpu.py',
   'PYMODULE'),
  ('pygments.lexers.ampl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\ampl.py',
   'PYMODULE'),
  ('pygments.lexers.apdlexer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\apdlexer.py',
   'PYMODULE'),
  ('pygments.lexers.apl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\apl.py',
   'PYMODULE'),
  ('pygments.lexers.archetype',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\archetype.py',
   'PYMODULE'),
  ('pygments.lexers.arrow',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\arrow.py',
   'PYMODULE'),
  ('pygments.lexers.arturo',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\arturo.py',
   'PYMODULE'),
  ('pygments.lexers.asc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\asc.py',
   'PYMODULE'),
  ('pygments.lexers.asm',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\asm.py',
   'PYMODULE'),
  ('pygments.lexers.asn1',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\asn1.py',
   'PYMODULE'),
  ('pygments.lexers.automation',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\automation.py',
   'PYMODULE'),
  ('pygments.lexers.bare',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\bare.py',
   'PYMODULE'),
  ('pygments.lexers.basic',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\basic.py',
   'PYMODULE'),
  ('pygments.lexers.bdd',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\bdd.py',
   'PYMODULE'),
  ('pygments.lexers.berry',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\berry.py',
   'PYMODULE'),
  ('pygments.lexers.bibtex',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\bibtex.py',
   'PYMODULE'),
  ('pygments.lexers.blueprint',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\blueprint.py',
   'PYMODULE'),
  ('pygments.lexers.boa',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\boa.py',
   'PYMODULE'),
  ('pygments.lexers.bqn',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\bqn.py',
   'PYMODULE'),
  ('pygments.lexers.business',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\business.py',
   'PYMODULE'),
  ('pygments.lexers.c_cpp',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\c_cpp.py',
   'PYMODULE'),
  ('pygments.lexers.c_like',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\c_like.py',
   'PYMODULE'),
  ('pygments.lexers.capnproto',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\capnproto.py',
   'PYMODULE'),
  ('pygments.lexers.carbon',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\carbon.py',
   'PYMODULE'),
  ('pygments.lexers.cddl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\cddl.py',
   'PYMODULE'),
  ('pygments.lexers.chapel',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\chapel.py',
   'PYMODULE'),
  ('pygments.lexers.clean',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\clean.py',
   'PYMODULE'),
  ('pygments.lexers.codeql',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\codeql.py',
   'PYMODULE'),
  ('pygments.lexers.comal',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\comal.py',
   'PYMODULE'),
  ('pygments.lexers.compiled',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\compiled.py',
   'PYMODULE'),
  ('pygments.lexers.configs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\configs.py',
   'PYMODULE'),
  ('pygments.lexers.console',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\console.py',
   'PYMODULE'),
  ('pygments.lexers.cplint',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\cplint.py',
   'PYMODULE'),
  ('pygments.lexers.crystal',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\crystal.py',
   'PYMODULE'),
  ('pygments.lexers.csound',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\csound.py',
   'PYMODULE'),
  ('pygments.lexers.css',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\css.py',
   'PYMODULE'),
  ('pygments.lexers.d',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\d.py',
   'PYMODULE'),
  ('pygments.lexers.dalvik',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\dalvik.py',
   'PYMODULE'),
  ('pygments.lexers.data',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\data.py',
   'PYMODULE'),
  ('pygments.lexers.dax',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\dax.py',
   'PYMODULE'),
  ('pygments.lexers.devicetree',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\devicetree.py',
   'PYMODULE'),
  ('pygments.lexers.diff',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\diff.py',
   'PYMODULE'),
  ('pygments.lexers.dns',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\dns.py',
   'PYMODULE'),
  ('pygments.lexers.dotnet',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\dotnet.py',
   'PYMODULE'),
  ('pygments.lexers.dsls',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\dsls.py',
   'PYMODULE'),
  ('pygments.lexers.dylan',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\dylan.py',
   'PYMODULE'),
  ('pygments.lexers.ecl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\ecl.py',
   'PYMODULE'),
  ('pygments.lexers.eiffel',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\eiffel.py',
   'PYMODULE'),
  ('pygments.lexers.elm',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\elm.py',
   'PYMODULE'),
  ('pygments.lexers.elpi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\elpi.py',
   'PYMODULE'),
  ('pygments.lexers.email',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\email.py',
   'PYMODULE'),
  ('pygments.lexers.erlang',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\erlang.py',
   'PYMODULE'),
  ('pygments.lexers.esoteric',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\esoteric.py',
   'PYMODULE'),
  ('pygments.lexers.ezhil',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\ezhil.py',
   'PYMODULE'),
  ('pygments.lexers.factor',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\factor.py',
   'PYMODULE'),
  ('pygments.lexers.fantom',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\fantom.py',
   'PYMODULE'),
  ('pygments.lexers.felix',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\felix.py',
   'PYMODULE'),
  ('pygments.lexers.fift',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\fift.py',
   'PYMODULE'),
  ('pygments.lexers.floscript',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\floscript.py',
   'PYMODULE'),
  ('pygments.lexers.forth',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\forth.py',
   'PYMODULE'),
  ('pygments.lexers.fortran',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\fortran.py',
   'PYMODULE'),
  ('pygments.lexers.foxpro',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\foxpro.py',
   'PYMODULE'),
  ('pygments.lexers.freefem',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\freefem.py',
   'PYMODULE'),
  ('pygments.lexers.func',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\func.py',
   'PYMODULE'),
  ('pygments.lexers.functional',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\functional.py',
   'PYMODULE'),
  ('pygments.lexers.futhark',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\futhark.py',
   'PYMODULE'),
  ('pygments.lexers.gcodelexer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\gcodelexer.py',
   'PYMODULE'),
  ('pygments.lexers.gdscript',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\gdscript.py',
   'PYMODULE'),
  ('pygments.lexers.gleam',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\gleam.py',
   'PYMODULE'),
  ('pygments.lexers.go',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\go.py',
   'PYMODULE'),
  ('pygments.lexers.grammar_notation',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\grammar_notation.py',
   'PYMODULE'),
  ('pygments.lexers.graph',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\graph.py',
   'PYMODULE'),
  ('pygments.lexers.graphics',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\graphics.py',
   'PYMODULE'),
  ('pygments.lexers.graphql',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\graphql.py',
   'PYMODULE'),
  ('pygments.lexers.graphviz',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\graphviz.py',
   'PYMODULE'),
  ('pygments.lexers.gsql',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\gsql.py',
   'PYMODULE'),
  ('pygments.lexers.hare',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\hare.py',
   'PYMODULE'),
  ('pygments.lexers.haskell',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\haskell.py',
   'PYMODULE'),
  ('pygments.lexers.haxe',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\haxe.py',
   'PYMODULE'),
  ('pygments.lexers.hdl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\hdl.py',
   'PYMODULE'),
  ('pygments.lexers.hexdump',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\hexdump.py',
   'PYMODULE'),
  ('pygments.lexers.html',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\html.py',
   'PYMODULE'),
  ('pygments.lexers.idl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\idl.py',
   'PYMODULE'),
  ('pygments.lexers.igor',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\igor.py',
   'PYMODULE'),
  ('pygments.lexers.inferno',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\inferno.py',
   'PYMODULE'),
  ('pygments.lexers.installers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\installers.py',
   'PYMODULE'),
  ('pygments.lexers.int_fiction',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\int_fiction.py',
   'PYMODULE'),
  ('pygments.lexers.iolang',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\iolang.py',
   'PYMODULE'),
  ('pygments.lexers.j',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\j.py',
   'PYMODULE'),
  ('pygments.lexers.javascript',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\javascript.py',
   'PYMODULE'),
  ('pygments.lexers.jmespath',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\jmespath.py',
   'PYMODULE'),
  ('pygments.lexers.jslt',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\jslt.py',
   'PYMODULE'),
  ('pygments.lexers.json5',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\json5.py',
   'PYMODULE'),
  ('pygments.lexers.jsonnet',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\jsonnet.py',
   'PYMODULE'),
  ('pygments.lexers.jsx',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\jsx.py',
   'PYMODULE'),
  ('pygments.lexers.julia',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\julia.py',
   'PYMODULE'),
  ('pygments.lexers.jvm',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\jvm.py',
   'PYMODULE'),
  ('pygments.lexers.kuin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\kuin.py',
   'PYMODULE'),
  ('pygments.lexers.kusto',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\kusto.py',
   'PYMODULE'),
  ('pygments.lexers.ldap',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\ldap.py',
   'PYMODULE'),
  ('pygments.lexers.lean',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\lean.py',
   'PYMODULE'),
  ('pygments.lexers.lilypond',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\lilypond.py',
   'PYMODULE'),
  ('pygments.lexers.lisp',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\lisp.py',
   'PYMODULE'),
  ('pygments.lexers.macaulay2',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\macaulay2.py',
   'PYMODULE'),
  ('pygments.lexers.make',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\make.py',
   'PYMODULE'),
  ('pygments.lexers.maple',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\maple.py',
   'PYMODULE'),
  ('pygments.lexers.markup',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\markup.py',
   'PYMODULE'),
  ('pygments.lexers.math',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\math.py',
   'PYMODULE'),
  ('pygments.lexers.matlab',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\matlab.py',
   'PYMODULE'),
  ('pygments.lexers.maxima',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\maxima.py',
   'PYMODULE'),
  ('pygments.lexers.meson',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\meson.py',
   'PYMODULE'),
  ('pygments.lexers.mime',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\mime.py',
   'PYMODULE'),
  ('pygments.lexers.minecraft',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\minecraft.py',
   'PYMODULE'),
  ('pygments.lexers.mips',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\mips.py',
   'PYMODULE'),
  ('pygments.lexers.ml',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\ml.py',
   'PYMODULE'),
  ('pygments.lexers.modeling',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\modeling.py',
   'PYMODULE'),
  ('pygments.lexers.modula2',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\modula2.py',
   'PYMODULE'),
  ('pygments.lexers.mojo',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\mojo.py',
   'PYMODULE'),
  ('pygments.lexers.monte',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\monte.py',
   'PYMODULE'),
  ('pygments.lexers.mosel',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\mosel.py',
   'PYMODULE'),
  ('pygments.lexers.ncl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\ncl.py',
   'PYMODULE'),
  ('pygments.lexers.nimrod',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\nimrod.py',
   'PYMODULE'),
  ('pygments.lexers.nit',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\nit.py',
   'PYMODULE'),
  ('pygments.lexers.nix',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\nix.py',
   'PYMODULE'),
  ('pygments.lexers.numbair',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\numbair.py',
   'PYMODULE'),
  ('pygments.lexers.oberon',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\oberon.py',
   'PYMODULE'),
  ('pygments.lexers.objective',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\objective.py',
   'PYMODULE'),
  ('pygments.lexers.ooc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\ooc.py',
   'PYMODULE'),
  ('pygments.lexers.openscad',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\openscad.py',
   'PYMODULE'),
  ('pygments.lexers.other',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\other.py',
   'PYMODULE'),
  ('pygments.lexers.parasail',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\parasail.py',
   'PYMODULE'),
  ('pygments.lexers.parsers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\parsers.py',
   'PYMODULE'),
  ('pygments.lexers.pascal',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\pascal.py',
   'PYMODULE'),
  ('pygments.lexers.pawn',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\pawn.py',
   'PYMODULE'),
  ('pygments.lexers.pddl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\pddl.py',
   'PYMODULE'),
  ('pygments.lexers.perl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\perl.py',
   'PYMODULE'),
  ('pygments.lexers.phix',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\phix.py',
   'PYMODULE'),
  ('pygments.lexers.php',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\php.py',
   'PYMODULE'),
  ('pygments.lexers.pointless',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\pointless.py',
   'PYMODULE'),
  ('pygments.lexers.pony',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\pony.py',
   'PYMODULE'),
  ('pygments.lexers.praat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\praat.py',
   'PYMODULE'),
  ('pygments.lexers.procfile',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\procfile.py',
   'PYMODULE'),
  ('pygments.lexers.prolog',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\prolog.py',
   'PYMODULE'),
  ('pygments.lexers.promql',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\promql.py',
   'PYMODULE'),
  ('pygments.lexers.prql',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\prql.py',
   'PYMODULE'),
  ('pygments.lexers.ptx',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\ptx.py',
   'PYMODULE'),
  ('pygments.lexers.python',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\python.py',
   'PYMODULE'),
  ('pygments.lexers.q',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\q.py',
   'PYMODULE'),
  ('pygments.lexers.qlik',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\qlik.py',
   'PYMODULE'),
  ('pygments.lexers.qvt',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\qvt.py',
   'PYMODULE'),
  ('pygments.lexers.r',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\r.py',
   'PYMODULE'),
  ('pygments.lexers.rdf',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\rdf.py',
   'PYMODULE'),
  ('pygments.lexers.rebol',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\rebol.py',
   'PYMODULE'),
  ('pygments.lexers.rego',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\rego.py',
   'PYMODULE'),
  ('pygments.lexers.resource',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\resource.py',
   'PYMODULE'),
  ('pygments.lexers.ride',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\ride.py',
   'PYMODULE'),
  ('pygments.lexers.rita',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\rita.py',
   'PYMODULE'),
  ('pygments.lexers.rnc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\rnc.py',
   'PYMODULE'),
  ('pygments.lexers.roboconf',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\roboconf.py',
   'PYMODULE'),
  ('pygments.lexers.robotframework',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\robotframework.py',
   'PYMODULE'),
  ('pygments.lexers.ruby',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\ruby.py',
   'PYMODULE'),
  ('pygments.lexers.rust',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\rust.py',
   'PYMODULE'),
  ('pygments.lexers.sas',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\sas.py',
   'PYMODULE'),
  ('pygments.lexers.savi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\savi.py',
   'PYMODULE'),
  ('pygments.lexers.scdoc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\scdoc.py',
   'PYMODULE'),
  ('pygments.lexers.scripting',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\scripting.py',
   'PYMODULE'),
  ('pygments.lexers.sgf',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\sgf.py',
   'PYMODULE'),
  ('pygments.lexers.shell',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\shell.py',
   'PYMODULE'),
  ('pygments.lexers.sieve',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\sieve.py',
   'PYMODULE'),
  ('pygments.lexers.slash',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\slash.py',
   'PYMODULE'),
  ('pygments.lexers.smalltalk',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\smalltalk.py',
   'PYMODULE'),
  ('pygments.lexers.smithy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\smithy.py',
   'PYMODULE'),
  ('pygments.lexers.smv',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\smv.py',
   'PYMODULE'),
  ('pygments.lexers.snobol',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\snobol.py',
   'PYMODULE'),
  ('pygments.lexers.solidity',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\solidity.py',
   'PYMODULE'),
  ('pygments.lexers.soong',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\soong.py',
   'PYMODULE'),
  ('pygments.lexers.sophia',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\sophia.py',
   'PYMODULE'),
  ('pygments.lexers.special',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\special.py',
   'PYMODULE'),
  ('pygments.lexers.spice',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\spice.py',
   'PYMODULE'),
  ('pygments.lexers.sql',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\sql.py',
   'PYMODULE'),
  ('pygments.lexers.srcinfo',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\srcinfo.py',
   'PYMODULE'),
  ('pygments.lexers.stata',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\stata.py',
   'PYMODULE'),
  ('pygments.lexers.supercollider',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\supercollider.py',
   'PYMODULE'),
  ('pygments.lexers.tablegen',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\tablegen.py',
   'PYMODULE'),
  ('pygments.lexers.tact',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\tact.py',
   'PYMODULE'),
  ('pygments.lexers.tal',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\tal.py',
   'PYMODULE'),
  ('pygments.lexers.tcl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\tcl.py',
   'PYMODULE'),
  ('pygments.lexers.teal',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\teal.py',
   'PYMODULE'),
  ('pygments.lexers.templates',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\templates.py',
   'PYMODULE'),
  ('pygments.lexers.teraterm',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\teraterm.py',
   'PYMODULE'),
  ('pygments.lexers.testing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\testing.py',
   'PYMODULE'),
  ('pygments.lexers.text',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\text.py',
   'PYMODULE'),
  ('pygments.lexers.textedit',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\textedit.py',
   'PYMODULE'),
  ('pygments.lexers.textfmts',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\textfmts.py',
   'PYMODULE'),
  ('pygments.lexers.theorem',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\theorem.py',
   'PYMODULE'),
  ('pygments.lexers.thingsdb',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\thingsdb.py',
   'PYMODULE'),
  ('pygments.lexers.tlb',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\tlb.py',
   'PYMODULE'),
  ('pygments.lexers.tls',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\tls.py',
   'PYMODULE'),
  ('pygments.lexers.tnt',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\tnt.py',
   'PYMODULE'),
  ('pygments.lexers.trafficscript',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\trafficscript.py',
   'PYMODULE'),
  ('pygments.lexers.typoscript',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\typoscript.py',
   'PYMODULE'),
  ('pygments.lexers.typst',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\typst.py',
   'PYMODULE'),
  ('pygments.lexers.ul4',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\ul4.py',
   'PYMODULE'),
  ('pygments.lexers.unicon',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\unicon.py',
   'PYMODULE'),
  ('pygments.lexers.urbi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\urbi.py',
   'PYMODULE'),
  ('pygments.lexers.usd',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\usd.py',
   'PYMODULE'),
  ('pygments.lexers.varnish',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\varnish.py',
   'PYMODULE'),
  ('pygments.lexers.verification',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\verification.py',
   'PYMODULE'),
  ('pygments.lexers.verifpal',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\verifpal.py',
   'PYMODULE'),
  ('pygments.lexers.vip',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\vip.py',
   'PYMODULE'),
  ('pygments.lexers.vyper',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\vyper.py',
   'PYMODULE'),
  ('pygments.lexers.web',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\web.py',
   'PYMODULE'),
  ('pygments.lexers.webassembly',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\webassembly.py',
   'PYMODULE'),
  ('pygments.lexers.webidl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\webidl.py',
   'PYMODULE'),
  ('pygments.lexers.webmisc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\webmisc.py',
   'PYMODULE'),
  ('pygments.lexers.wgsl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\wgsl.py',
   'PYMODULE'),
  ('pygments.lexers.whiley',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\whiley.py',
   'PYMODULE'),
  ('pygments.lexers.wowtoc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\wowtoc.py',
   'PYMODULE'),
  ('pygments.lexers.wren',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\wren.py',
   'PYMODULE'),
  ('pygments.lexers.x10',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\x10.py',
   'PYMODULE'),
  ('pygments.lexers.xorg',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\xorg.py',
   'PYMODULE'),
  ('pygments.lexers.yang',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\yang.py',
   'PYMODULE'),
  ('pygments.lexers.yara',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\yara.py',
   'PYMODULE'),
  ('pygments.lexers.zig',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\lexers\\zig.py',
   'PYMODULE'),
  ('pygments.modeline',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\modeline.py',
   'PYMODULE'),
  ('pygments.plugin',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\plugin.py',
   'PYMODULE'),
  ('pygments.regexopt',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\regexopt.py',
   'PYMODULE'),
  ('pygments.scanner',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\scanner.py',
   'PYMODULE'),
  ('pygments.style',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\style.py',
   'PYMODULE'),
  ('pygments.styles',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\__init__.py',
   'PYMODULE'),
  ('pygments.styles._mapping',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\_mapping.py',
   'PYMODULE'),
  ('pygments.styles.abap',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\abap.py',
   'PYMODULE'),
  ('pygments.styles.algol',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\algol.py',
   'PYMODULE'),
  ('pygments.styles.algol_nu',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\algol_nu.py',
   'PYMODULE'),
  ('pygments.styles.arduino',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\arduino.py',
   'PYMODULE'),
  ('pygments.styles.autumn',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\autumn.py',
   'PYMODULE'),
  ('pygments.styles.borland',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\borland.py',
   'PYMODULE'),
  ('pygments.styles.bw',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\bw.py',
   'PYMODULE'),
  ('pygments.styles.coffee',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\coffee.py',
   'PYMODULE'),
  ('pygments.styles.colorful',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\colorful.py',
   'PYMODULE'),
  ('pygments.styles.default',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\default.py',
   'PYMODULE'),
  ('pygments.styles.dracula',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\dracula.py',
   'PYMODULE'),
  ('pygments.styles.emacs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\emacs.py',
   'PYMODULE'),
  ('pygments.styles.friendly',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\friendly.py',
   'PYMODULE'),
  ('pygments.styles.friendly_grayscale',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\friendly_grayscale.py',
   'PYMODULE'),
  ('pygments.styles.fruity',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\fruity.py',
   'PYMODULE'),
  ('pygments.styles.gh_dark',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\gh_dark.py',
   'PYMODULE'),
  ('pygments.styles.gruvbox',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\gruvbox.py',
   'PYMODULE'),
  ('pygments.styles.igor',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\igor.py',
   'PYMODULE'),
  ('pygments.styles.inkpot',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\inkpot.py',
   'PYMODULE'),
  ('pygments.styles.lightbulb',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\lightbulb.py',
   'PYMODULE'),
  ('pygments.styles.lilypond',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\lilypond.py',
   'PYMODULE'),
  ('pygments.styles.lovelace',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\lovelace.py',
   'PYMODULE'),
  ('pygments.styles.manni',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\manni.py',
   'PYMODULE'),
  ('pygments.styles.material',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\material.py',
   'PYMODULE'),
  ('pygments.styles.monokai',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\monokai.py',
   'PYMODULE'),
  ('pygments.styles.murphy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\murphy.py',
   'PYMODULE'),
  ('pygments.styles.native',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\native.py',
   'PYMODULE'),
  ('pygments.styles.nord',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\nord.py',
   'PYMODULE'),
  ('pygments.styles.onedark',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\onedark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_dark',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\paraiso_dark.py',
   'PYMODULE'),
  ('pygments.styles.paraiso_light',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\paraiso_light.py',
   'PYMODULE'),
  ('pygments.styles.pastie',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\pastie.py',
   'PYMODULE'),
  ('pygments.styles.perldoc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\perldoc.py',
   'PYMODULE'),
  ('pygments.styles.rainbow_dash',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\rainbow_dash.py',
   'PYMODULE'),
  ('pygments.styles.rrt',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\rrt.py',
   'PYMODULE'),
  ('pygments.styles.sas',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\sas.py',
   'PYMODULE'),
  ('pygments.styles.solarized',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\solarized.py',
   'PYMODULE'),
  ('pygments.styles.staroffice',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\staroffice.py',
   'PYMODULE'),
  ('pygments.styles.stata_dark',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\stata_dark.py',
   'PYMODULE'),
  ('pygments.styles.stata_light',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\stata_light.py',
   'PYMODULE'),
  ('pygments.styles.tango',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\tango.py',
   'PYMODULE'),
  ('pygments.styles.trac',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\trac.py',
   'PYMODULE'),
  ('pygments.styles.vim',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\vim.py',
   'PYMODULE'),
  ('pygments.styles.vs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\vs.py',
   'PYMODULE'),
  ('pygments.styles.xcode',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\xcode.py',
   'PYMODULE'),
  ('pygments.styles.zenburn',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\styles\\zenburn.py',
   'PYMODULE'),
  ('pygments.token',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\token.py',
   'PYMODULE'),
  ('pygments.unistring',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\unistring.py',
   'PYMODULE'),
  ('pygments.util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pygments\\util.py',
   'PYMODULE'),
  ('pythoncom',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pythonnet',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\pythonnet\\__init__.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('pywintypes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('queue',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\random.py',
   'PYMODULE'),
  ('redis',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\__init__.py',
   'PYMODULE'),
  ('redis._parsers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\_parsers\\__init__.py',
   'PYMODULE'),
  ('redis._parsers.base',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\_parsers\\base.py',
   'PYMODULE'),
  ('redis._parsers.commands',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\_parsers\\commands.py',
   'PYMODULE'),
  ('redis._parsers.encoders',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\_parsers\\encoders.py',
   'PYMODULE'),
  ('redis._parsers.helpers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\_parsers\\helpers.py',
   'PYMODULE'),
  ('redis._parsers.hiredis',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\_parsers\\hiredis.py',
   'PYMODULE'),
  ('redis._parsers.resp2',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\_parsers\\resp2.py',
   'PYMODULE'),
  ('redis._parsers.resp3',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\_parsers\\resp3.py',
   'PYMODULE'),
  ('redis._parsers.socket',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\_parsers\\socket.py',
   'PYMODULE'),
  ('redis.asyncio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\asyncio\\__init__.py',
   'PYMODULE'),
  ('redis.asyncio.client',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\asyncio\\client.py',
   'PYMODULE'),
  ('redis.asyncio.cluster',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\asyncio\\cluster.py',
   'PYMODULE'),
  ('redis.asyncio.connection',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\asyncio\\connection.py',
   'PYMODULE'),
  ('redis.asyncio.lock',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\asyncio\\lock.py',
   'PYMODULE'),
  ('redis.asyncio.retry',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\asyncio\\retry.py',
   'PYMODULE'),
  ('redis.asyncio.sentinel',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\asyncio\\sentinel.py',
   'PYMODULE'),
  ('redis.asyncio.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\asyncio\\utils.py',
   'PYMODULE'),
  ('redis.backoff',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\backoff.py',
   'PYMODULE'),
  ('redis.cache',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\cache.py',
   'PYMODULE'),
  ('redis.client',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\client.py',
   'PYMODULE'),
  ('redis.cluster',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\cluster.py',
   'PYMODULE'),
  ('redis.commands',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\__init__.py',
   'PYMODULE'),
  ('redis.commands.bf',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\bf\\__init__.py',
   'PYMODULE'),
  ('redis.commands.bf.commands',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\bf\\commands.py',
   'PYMODULE'),
  ('redis.commands.bf.info',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\bf\\info.py',
   'PYMODULE'),
  ('redis.commands.cluster',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\cluster.py',
   'PYMODULE'),
  ('redis.commands.core',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\core.py',
   'PYMODULE'),
  ('redis.commands.graph',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\graph\\__init__.py',
   'PYMODULE'),
  ('redis.commands.graph.commands',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\graph\\commands.py',
   'PYMODULE'),
  ('redis.commands.graph.edge',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\graph\\edge.py',
   'PYMODULE'),
  ('redis.commands.graph.exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\graph\\exceptions.py',
   'PYMODULE'),
  ('redis.commands.graph.execution_plan',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\graph\\execution_plan.py',
   'PYMODULE'),
  ('redis.commands.graph.node',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\graph\\node.py',
   'PYMODULE'),
  ('redis.commands.graph.path',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\graph\\path.py',
   'PYMODULE'),
  ('redis.commands.graph.query_result',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\graph\\query_result.py',
   'PYMODULE'),
  ('redis.commands.helpers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\helpers.py',
   'PYMODULE'),
  ('redis.commands.json',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\json\\__init__.py',
   'PYMODULE'),
  ('redis.commands.json._util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\json\\_util.py',
   'PYMODULE'),
  ('redis.commands.json.commands',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\json\\commands.py',
   'PYMODULE'),
  ('redis.commands.json.decoders',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\json\\decoders.py',
   'PYMODULE'),
  ('redis.commands.json.path',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\json\\path.py',
   'PYMODULE'),
  ('redis.commands.redismodules',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\redismodules.py',
   'PYMODULE'),
  ('redis.commands.search',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\search\\__init__.py',
   'PYMODULE'),
  ('redis.commands.search._util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\search\\_util.py',
   'PYMODULE'),
  ('redis.commands.search.aggregation',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\search\\aggregation.py',
   'PYMODULE'),
  ('redis.commands.search.commands',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\search\\commands.py',
   'PYMODULE'),
  ('redis.commands.search.document',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\search\\document.py',
   'PYMODULE'),
  ('redis.commands.search.field',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\search\\field.py',
   'PYMODULE'),
  ('redis.commands.search.indexDefinition',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\search\\indexDefinition.py',
   'PYMODULE'),
  ('redis.commands.search.query',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\search\\query.py',
   'PYMODULE'),
  ('redis.commands.search.result',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\search\\result.py',
   'PYMODULE'),
  ('redis.commands.search.suggestion',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\search\\suggestion.py',
   'PYMODULE'),
  ('redis.commands.sentinel',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\sentinel.py',
   'PYMODULE'),
  ('redis.commands.timeseries',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\timeseries\\__init__.py',
   'PYMODULE'),
  ('redis.commands.timeseries.commands',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\timeseries\\commands.py',
   'PYMODULE'),
  ('redis.commands.timeseries.info',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\timeseries\\info.py',
   'PYMODULE'),
  ('redis.commands.timeseries.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\commands\\timeseries\\utils.py',
   'PYMODULE'),
  ('redis.connection',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\connection.py',
   'PYMODULE'),
  ('redis.crc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\crc.py',
   'PYMODULE'),
  ('redis.credentials',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\credentials.py',
   'PYMODULE'),
  ('redis.exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\exceptions.py',
   'PYMODULE'),
  ('redis.lock',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\lock.py',
   'PYMODULE'),
  ('redis.ocsp',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\ocsp.py',
   'PYMODULE'),
  ('redis.retry',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\retry.py',
   'PYMODULE'),
  ('redis.sentinel',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\sentinel.py',
   'PYMODULE'),
  ('redis.typing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\typing.py',
   'PYMODULE'),
  ('redis.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\redis\\utils.py',
   'PYMODULE'),
  ('referencing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\referencing\\__init__.py',
   'PYMODULE'),
  ('referencing._attrs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\referencing\\_attrs.py',
   'PYMODULE'),
  ('referencing._core',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\referencing\\_core.py',
   'PYMODULE'),
  ('referencing.exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\referencing\\exceptions.py',
   'PYMODULE'),
  ('referencing.jsonschema',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\referencing\\jsonschema.py',
   'PYMODULE'),
  ('referencing.typing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\referencing\\typing.py',
   'PYMODULE'),
  ('requests',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rich',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\__init__.py',
   'PYMODULE'),
  ('rich.__main__',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\__main__.py',
   'PYMODULE'),
  ('rich._cell_widths',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_cell_widths.py',
   'PYMODULE'),
  ('rich._emoji_codes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_emoji_codes.py',
   'PYMODULE'),
  ('rich._emoji_replace',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_emoji_replace.py',
   'PYMODULE'),
  ('rich._export_format',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_export_format.py',
   'PYMODULE'),
  ('rich._extension',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_extension.py',
   'PYMODULE'),
  ('rich._fileno',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_fileno.py',
   'PYMODULE'),
  ('rich._inspect',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_inspect.py',
   'PYMODULE'),
  ('rich._log_render',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_log_render.py',
   'PYMODULE'),
  ('rich._loop',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_loop.py',
   'PYMODULE'),
  ('rich._null_file',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_null_file.py',
   'PYMODULE'),
  ('rich._palettes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_palettes.py',
   'PYMODULE'),
  ('rich._pick',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_pick.py',
   'PYMODULE'),
  ('rich._ratio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_ratio.py',
   'PYMODULE'),
  ('rich._spinners',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_spinners.py',
   'PYMODULE'),
  ('rich._stack',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_stack.py',
   'PYMODULE'),
  ('rich._timer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_timer.py',
   'PYMODULE'),
  ('rich._win32_console',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_win32_console.py',
   'PYMODULE'),
  ('rich._windows',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_windows.py',
   'PYMODULE'),
  ('rich._windows_renderer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_windows_renderer.py',
   'PYMODULE'),
  ('rich._wrap',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\_wrap.py',
   'PYMODULE'),
  ('rich.abc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\abc.py',
   'PYMODULE'),
  ('rich.align',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\align.py',
   'PYMODULE'),
  ('rich.ansi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\ansi.py',
   'PYMODULE'),
  ('rich.box',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\box.py',
   'PYMODULE'),
  ('rich.cells',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\cells.py',
   'PYMODULE'),
  ('rich.color',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\color.py',
   'PYMODULE'),
  ('rich.color_triplet',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\color_triplet.py',
   'PYMODULE'),
  ('rich.columns',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\columns.py',
   'PYMODULE'),
  ('rich.console',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\console.py',
   'PYMODULE'),
  ('rich.constrain',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\constrain.py',
   'PYMODULE'),
  ('rich.containers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\containers.py',
   'PYMODULE'),
  ('rich.control',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\control.py',
   'PYMODULE'),
  ('rich.default_styles',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\default_styles.py',
   'PYMODULE'),
  ('rich.emoji',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\emoji.py',
   'PYMODULE'),
  ('rich.errors',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\errors.py',
   'PYMODULE'),
  ('rich.file_proxy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\file_proxy.py',
   'PYMODULE'),
  ('rich.filesize',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\filesize.py',
   'PYMODULE'),
  ('rich.highlighter',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\highlighter.py',
   'PYMODULE'),
  ('rich.json',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\json.py',
   'PYMODULE'),
  ('rich.jupyter',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\jupyter.py',
   'PYMODULE'),
  ('rich.live',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\live.py',
   'PYMODULE'),
  ('rich.live_render',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\live_render.py',
   'PYMODULE'),
  ('rich.markdown',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\markdown.py',
   'PYMODULE'),
  ('rich.markup',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\markup.py',
   'PYMODULE'),
  ('rich.measure',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\measure.py',
   'PYMODULE'),
  ('rich.padding',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\padding.py',
   'PYMODULE'),
  ('rich.pager',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\pager.py',
   'PYMODULE'),
  ('rich.palette',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\palette.py',
   'PYMODULE'),
  ('rich.panel',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\panel.py',
   'PYMODULE'),
  ('rich.pretty',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\pretty.py',
   'PYMODULE'),
  ('rich.progress',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\progress.py',
   'PYMODULE'),
  ('rich.progress_bar',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\progress_bar.py',
   'PYMODULE'),
  ('rich.protocol',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\protocol.py',
   'PYMODULE'),
  ('rich.region',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\region.py',
   'PYMODULE'),
  ('rich.repr',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\repr.py',
   'PYMODULE'),
  ('rich.rule',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\rule.py',
   'PYMODULE'),
  ('rich.scope',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\scope.py',
   'PYMODULE'),
  ('rich.screen',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\screen.py',
   'PYMODULE'),
  ('rich.segment',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\segment.py',
   'PYMODULE'),
  ('rich.spinner',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\spinner.py',
   'PYMODULE'),
  ('rich.status',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\status.py',
   'PYMODULE'),
  ('rich.style',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\style.py',
   'PYMODULE'),
  ('rich.styled',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\styled.py',
   'PYMODULE'),
  ('rich.syntax',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\syntax.py',
   'PYMODULE'),
  ('rich.table',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\table.py',
   'PYMODULE'),
  ('rich.terminal_theme',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\terminal_theme.py',
   'PYMODULE'),
  ('rich.text',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\text.py',
   'PYMODULE'),
  ('rich.theme',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\theme.py',
   'PYMODULE'),
  ('rich.themes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\themes.py',
   'PYMODULE'),
  ('rich.traceback',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rich\\traceback.py',
   'PYMODULE'),
  ('rlcompleter',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\rlcompleter.py',
   'PYMODULE'),
  ('rpds',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\rpds\\__init__.py',
   'PYMODULE'),
  ('runpy',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py38',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.zosccompiler',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_distutils\\zosccompiler.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat.py38',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat.py39',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.functional',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\functional.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.future',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\future\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.future.adapters',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\future\\adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\signal.py',
   'PYMODULE'),
  ('simple_websocket',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\simple_websocket\\__init__.py',
   'PYMODULE'),
  ('simple_websocket.aiows',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\simple_websocket\\aiows.py',
   'PYMODULE'),
  ('simple_websocket.asgi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\simple_websocket\\asgi.py',
   'PYMODULE'),
  ('simple_websocket.errors',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\simple_websocket\\errors.py',
   'PYMODULE'),
  ('simple_websocket.ws',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\simple_websocket\\ws.py',
   'PYMODULE'),
  ('site',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\site.py',
   'PYMODULE'),
  ('six',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('smtplib',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\smtplib.py',
   'PYMODULE'),
  ('sniffio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('socket',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\socket.py',
   'PYMODULE'),
  ('socket_handler',
   'D:\\testcode\\typegame改\\server\\socket_handler.py',
   'PYMODULE'),
  ('socketio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\__init__.py',
   'PYMODULE'),
  ('socketio.asgi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\asgi.py',
   'PYMODULE'),
  ('socketio.asyncio_aiopika_manager',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\asyncio_aiopika_manager.py',
   'PYMODULE'),
  ('socketio.asyncio_client',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\asyncio_client.py',
   'PYMODULE'),
  ('socketio.asyncio_manager',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\asyncio_manager.py',
   'PYMODULE'),
  ('socketio.asyncio_namespace',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\asyncio_namespace.py',
   'PYMODULE'),
  ('socketio.asyncio_pubsub_manager',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\asyncio_pubsub_manager.py',
   'PYMODULE'),
  ('socketio.asyncio_redis_manager',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\asyncio_redis_manager.py',
   'PYMODULE'),
  ('socketio.asyncio_server',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\asyncio_server.py',
   'PYMODULE'),
  ('socketio.base_manager',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\base_manager.py',
   'PYMODULE'),
  ('socketio.client',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\client.py',
   'PYMODULE'),
  ('socketio.exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\exceptions.py',
   'PYMODULE'),
  ('socketio.kafka_manager',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\kafka_manager.py',
   'PYMODULE'),
  ('socketio.kombu_manager',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\kombu_manager.py',
   'PYMODULE'),
  ('socketio.middleware',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\middleware.py',
   'PYMODULE'),
  ('socketio.msgpack_packet',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\msgpack_packet.py',
   'PYMODULE'),
  ('socketio.namespace',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\namespace.py',
   'PYMODULE'),
  ('socketio.packet',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\packet.py',
   'PYMODULE'),
  ('socketio.pubsub_manager',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\pubsub_manager.py',
   'PYMODULE'),
  ('socketio.redis_manager',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\redis_manager.py',
   'PYMODULE'),
  ('socketio.server',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\server.py',
   'PYMODULE'),
  ('socketio.tornado',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\tornado.py',
   'PYMODULE'),
  ('socketio.zmq_manager',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\socketio\\zmq_manager.py',
   'PYMODULE'),
  ('socketserver',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\socketserver.py',
   'PYMODULE'),
  ('sqlite3',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\ssl.py',
   'PYMODULE'),
  ('stack_data',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\stack_data\\__init__.py',
   'PYMODULE'),
  ('stack_data.core',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\stack_data\\core.py',
   'PYMODULE'),
  ('stack_data.formatting',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\stack_data\\formatting.py',
   'PYMODULE'),
  ('stack_data.serializing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\stack_data\\serializing.py',
   'PYMODULE'),
  ('stack_data.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\stack_data\\utils.py',
   'PYMODULE'),
  ('stack_data.version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\stack_data\\version.py',
   'PYMODULE'),
  ('statistics',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\threading.py',
   'PYMODULE'),
  ('threadpoolctl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\threadpoolctl.py',
   'PYMODULE'),
  ('timeit',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\timeit.py',
   'PYMODULE'),
  ('tkinter',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\tokenize.py',
   'PYMODULE'),
  ('tornado',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\__init__.py',
   'PYMODULE'),
  ('tornado._locale_data',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\_locale_data.py',
   'PYMODULE'),
  ('tornado.autoreload',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\autoreload.py',
   'PYMODULE'),
  ('tornado.concurrent',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\concurrent.py',
   'PYMODULE'),
  ('tornado.escape',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\escape.py',
   'PYMODULE'),
  ('tornado.gen',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\gen.py',
   'PYMODULE'),
  ('tornado.http1connection',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\http1connection.py',
   'PYMODULE'),
  ('tornado.httpclient',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\httpclient.py',
   'PYMODULE'),
  ('tornado.httpserver',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\httpserver.py',
   'PYMODULE'),
  ('tornado.httputil',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\httputil.py',
   'PYMODULE'),
  ('tornado.ioloop',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\ioloop.py',
   'PYMODULE'),
  ('tornado.iostream',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\iostream.py',
   'PYMODULE'),
  ('tornado.locale',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\locale.py',
   'PYMODULE'),
  ('tornado.locks',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\locks.py',
   'PYMODULE'),
  ('tornado.log',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\log.py',
   'PYMODULE'),
  ('tornado.netutil',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\netutil.py',
   'PYMODULE'),
  ('tornado.options',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\options.py',
   'PYMODULE'),
  ('tornado.platform',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\platform\\__init__.py',
   'PYMODULE'),
  ('tornado.platform.asyncio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\platform\\asyncio.py',
   'PYMODULE'),
  ('tornado.process',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\process.py',
   'PYMODULE'),
  ('tornado.queues',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\queues.py',
   'PYMODULE'),
  ('tornado.routing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\routing.py',
   'PYMODULE'),
  ('tornado.simple_httpclient',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\simple_httpclient.py',
   'PYMODULE'),
  ('tornado.tcpclient',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\tcpclient.py',
   'PYMODULE'),
  ('tornado.tcpserver',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\tcpserver.py',
   'PYMODULE'),
  ('tornado.template',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\template.py',
   'PYMODULE'),
  ('tornado.util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\util.py',
   'PYMODULE'),
  ('tornado.web',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\web.py',
   'PYMODULE'),
  ('tornado.websocket',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\tornado\\websocket.py',
   'PYMODULE'),
  ('tracemalloc',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('traitlets',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\traitlets\\__init__.py',
   'PYMODULE'),
  ('traitlets._version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\traitlets\\_version.py',
   'PYMODULE'),
  ('traitlets.config',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\traitlets\\config\\__init__.py',
   'PYMODULE'),
  ('traitlets.config.application',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\traitlets\\config\\application.py',
   'PYMODULE'),
  ('traitlets.config.argcomplete_config',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\traitlets\\config\\argcomplete_config.py',
   'PYMODULE'),
  ('traitlets.config.configurable',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\traitlets\\config\\configurable.py',
   'PYMODULE'),
  ('traitlets.config.loader',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\traitlets\\config\\loader.py',
   'PYMODULE'),
  ('traitlets.log',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\traitlets\\log.py',
   'PYMODULE'),
  ('traitlets.traitlets',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\traitlets\\traitlets.py',
   'PYMODULE'),
  ('traitlets.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\traitlets\\utils\\__init__.py',
   'PYMODULE'),
  ('traitlets.utils.bunch',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\traitlets\\utils\\bunch.py',
   'PYMODULE'),
  ('traitlets.utils.decorators',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\traitlets\\utils\\decorators.py',
   'PYMODULE'),
  ('traitlets.utils.descriptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\traitlets\\utils\\descriptions.py',
   'PYMODULE'),
  ('traitlets.utils.getargspec',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\traitlets\\utils\\getargspec.py',
   'PYMODULE'),
  ('traitlets.utils.importstring',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\traitlets\\utils\\importstring.py',
   'PYMODULE'),
  ('traitlets.utils.nested_update',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\traitlets\\utils\\nested_update.py',
   'PYMODULE'),
  ('traitlets.utils.sentinel',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\traitlets\\utils\\sentinel.py',
   'PYMODULE'),
  ('traitlets.utils.text',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\traitlets\\utils\\text.py',
   'PYMODULE'),
  ('traitlets.utils.warnings',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\traitlets\\utils\\warnings.py',
   'PYMODULE'),
  ('tty',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\uu.py',
   'PYMODULE'),
  ('uuid',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\uuid.py',
   'PYMODULE'),
  ('wave',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\wave.py',
   'PYMODULE'),
  ('wcwidth',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wcwidth\\__init__.py',
   'PYMODULE'),
  ('wcwidth.table_vs16',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wcwidth\\table_vs16.py',
   'PYMODULE'),
  ('wcwidth.table_wide',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wcwidth\\table_wide.py',
   'PYMODULE'),
  ('wcwidth.table_zero',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wcwidth\\table_zero.py',
   'PYMODULE'),
  ('wcwidth.unicode_versions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wcwidth\\unicode_versions.py',
   'PYMODULE'),
  ('wcwidth.wcwidth',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wcwidth\\wcwidth.py',
   'PYMODULE'),
  ('webbrowser',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\webbrowser.py',
   'PYMODULE'),
  ('werkzeug',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\datastructures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.filesystem',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\filesystem.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.local',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.dispatcher',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\middleware\\dispatcher.py',
   'PYMODULE'),
  ('werkzeug.middleware.http_proxy',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\middleware\\http_proxy.py',
   'PYMODULE'),
  ('werkzeug.middleware.lint',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\middleware\\lint.py',
   'PYMODULE'),
  ('werkzeug.middleware.profiler',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\middleware\\profiler.py',
   'PYMODULE'),
  ('werkzeug.middleware.proxy_fix',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\middleware\\proxy_fix.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\routing.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.testapp',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\testapp.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.useragents',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\useragents.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.accept',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\wrappers\\accept.py',
   'PYMODULE'),
  ('werkzeug.wrappers.auth',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\wrappers\\auth.py',
   'PYMODULE'),
  ('werkzeug.wrappers.base_request',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\wrappers\\base_request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.base_response',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\wrappers\\base_response.py',
   'PYMODULE'),
  ('werkzeug.wrappers.common_descriptors',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\wrappers\\common_descriptors.py',
   'PYMODULE'),
  ('werkzeug.wrappers.cors',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\wrappers\\cors.py',
   'PYMODULE'),
  ('werkzeug.wrappers.etag',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\wrappers\\etag.py',
   'PYMODULE'),
  ('werkzeug.wrappers.json',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\wrappers\\json.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wrappers.user_agent',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\wrappers\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('wheel',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\__init__.py',
   'PYMODULE'),
  ('wheel._bdist_wheel',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\_bdist_wheel.py',
   'PYMODULE'),
  ('wheel._setuptools_logging',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\_setuptools_logging.py',
   'PYMODULE'),
  ('wheel.cli',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('wheel.metadata',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\metadata.py',
   'PYMODULE'),
  ('wheel.util',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\util.py',
   'PYMODULE'),
  ('wheel.vendored',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('win32con',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('wsproto',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wsproto\\__init__.py',
   'PYMODULE'),
  ('wsproto.connection',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wsproto\\connection.py',
   'PYMODULE'),
  ('wsproto.events',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wsproto\\events.py',
   'PYMODULE'),
  ('wsproto.extensions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wsproto\\extensions.py',
   'PYMODULE'),
  ('wsproto.frame_protocol',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wsproto\\frame_protocol.py',
   'PYMODULE'),
  ('wsproto.handshake',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wsproto\\handshake.py',
   'PYMODULE'),
  ('wsproto.typing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wsproto\\typing.py',
   'PYMODULE'),
  ('wsproto.utilities',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\wsproto\\utilities.py',
   'PYMODULE'),
  ('xml',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('yaml',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'd:\\Program Files\\miniconda3\\envs\\test3.10\\lib\\zipimport.py',
   'PYMODULE'),
  ('zipp',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp._functools',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zipp\\_functools.py',
   'PYMODULE'),
  ('zipp.compat',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.glob',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zipp\\glob.py',
   'PYMODULE'),
  ('zmq',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\__init__.py',
   'PYMODULE'),
  ('zmq._future',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\_future.py',
   'PYMODULE'),
  ('zmq._typing',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\_typing.py',
   'PYMODULE'),
  ('zmq.asyncio',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\asyncio.py',
   'PYMODULE'),
  ('zmq.backend',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\backend\\__init__.py',
   'PYMODULE'),
  ('zmq.backend.cython',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\backend\\cython\\__init__.py',
   'PYMODULE'),
  ('zmq.backend.select',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\backend\\select.py',
   'PYMODULE'),
  ('zmq.constants',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\constants.py',
   'PYMODULE'),
  ('zmq.error',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\error.py',
   'PYMODULE'),
  ('zmq.eventloop',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\eventloop\\__init__.py',
   'PYMODULE'),
  ('zmq.eventloop.zmqstream',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\eventloop\\zmqstream.py',
   'PYMODULE'),
  ('zmq.green',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\green\\__init__.py',
   'PYMODULE'),
  ('zmq.green.core',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\green\\core.py',
   'PYMODULE'),
  ('zmq.green.device',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\green\\device.py',
   'PYMODULE'),
  ('zmq.green.poll',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\green\\poll.py',
   'PYMODULE'),
  ('zmq.sugar',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\sugar\\__init__.py',
   'PYMODULE'),
  ('zmq.sugar.attrsettr',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\sugar\\attrsettr.py',
   'PYMODULE'),
  ('zmq.sugar.context',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\sugar\\context.py',
   'PYMODULE'),
  ('zmq.sugar.frame',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\sugar\\frame.py',
   'PYMODULE'),
  ('zmq.sugar.poll',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\sugar\\poll.py',
   'PYMODULE'),
  ('zmq.sugar.socket',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\sugar\\socket.py',
   'PYMODULE'),
  ('zmq.sugar.stopwatch',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\sugar\\stopwatch.py',
   'PYMODULE'),
  ('zmq.sugar.tracker',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\sugar\\tracker.py',
   'PYMODULE'),
  ('zmq.sugar.version',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\sugar\\version.py',
   'PYMODULE'),
  ('zmq.utils',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\utils\\__init__.py',
   'PYMODULE'),
  ('zmq.utils.garbage',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\utils\\garbage.py',
   'PYMODULE'),
  ('zmq.utils.interop',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\utils\\interop.py',
   'PYMODULE'),
  ('zmq.utils.jsonapi',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\zmq\\utils\\jsonapi.py',
   'PYMODULE')])
