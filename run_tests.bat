@echo off
chcp 65001 >nul
echo ================================================================
echo 在线打字比赛系统 - 自动化测试程序
echo ================================================================
echo.

:MENU
echo 请选择测试类型：
echo.
echo 1. 快速测试 (10个客户端)
echo 2. 标准测试 (30个客户端)
echo 3. 压力测试 (60个客户端)
echo 4. 自定义测试
echo 5. 性能监控
echo 6. 查看帮助
echo 7. 退出
echo.
set /p choice=请输入选项 (1-7): 

if "%choice%"=="1" goto QUICK_TEST
if "%choice%"=="2" goto STANDARD_TEST
if "%choice%"=="3" goto STRESS_TEST
if "%choice%"=="4" goto CUSTOM_TEST
if "%choice%"=="5" goto PERFORMANCE_MONITOR
if "%choice%"=="6" goto HELP
if "%choice%"=="7" goto EXIT

echo 无效选项，请重新选择。
echo.
goto MENU

:QUICK_TEST
echo.
echo 开始快速测试...
echo ================================================================
python quick_test.py --clients 10
echo.
echo 快速测试完成！
pause
goto MENU

:STANDARD_TEST
echo.
echo 开始标准测试...
echo ================================================================
python test_concurrent_clients.py --clients 30
echo.
echo 标准测试完成！
pause
goto MENU

:STRESS_TEST
echo.
echo 开始压力测试...
echo ================================================================
echo 警告：压力测试将使用60个客户端，可能对系统造成较大负载。
set /p confirm=确认继续？(y/n): 
if /i "%confirm%"=="y" (
    python test_concurrent_clients.py --clients 60 --verbose
) else (
    echo 测试已取消。
)
echo.
echo 压力测试完成！
pause
goto MENU

:CUSTOM_TEST
echo.
echo 自定义测试设置
echo ================================================================
set /p server=服务器地址 (默认: http://localhost:5000): 
if "%server%"=="" set server=http://localhost:5000

set /p clients=客户端数量 (默认: 20): 
if "%clients%"=="" set clients=20

set /p verbose=详细输出？(y/n, 默认: n): 
if /i "%verbose%"=="y" (
    set verbose_flag=--verbose
) else (
    set verbose_flag=
)

echo.
echo 开始自定义测试...
echo 服务器: %server%
echo 客户端数量: %clients%
echo ================================================================
python test_concurrent_clients.py --server %server% --clients %clients% %verbose_flag%
echo.
echo 自定义测试完成！
pause
goto MENU

:PERFORMANCE_MONITOR
echo.
echo 性能监控设置
echo ================================================================
set /p interval=监控间隔秒数 (默认: 5): 
if "%interval%"=="" set interval=5

set /p output=输出文件名 (默认: 不保存): 
if "%output%"=="" (
    set output_flag=
) else (
    set output_flag=--output %output%
)

set /p server=服务器地址 (默认: http://localhost:5000): 
if "%server%"=="" set server=http://localhost:5000

echo.
echo 开始性能监控...
echo 监控间隔: %interval% 秒
echo 服务器: %server%
if not "%output%"=="" echo 输出文件: %output%
echo 按 Ctrl+C 停止监控
echo ================================================================
python performance_monitor.py --interval %interval% --server %server% %output_flag%
echo.
echo 性能监控完成！
pause
goto MENU

:HELP
echo.
echo 帮助信息
echo ================================================================
echo.
echo 使用前准备：
echo 1. 确保已安装Python 3.7+
echo 2. 安装依赖：pip install python-socketio[asyncio] psutil requests
echo 3. 启动服务端：python server/manager.py
echo 4. 在管理界面点击"启动服务器"
echo.
echo 测试流程：
echo 1. 选择并运行测试程序
echo 2. 等待客户端连接完成
echo 3. 在管理界面设置比赛文本
echo 4. 点击"创建比赛"然后"开始比赛"
echo 5. 观察测试过程和结果
echo.
echo 文件说明：
echo - test_concurrent_clients.py: 完整并发测试
echo - quick_test.py: 快速测试工具
echo - performance_monitor.py: 性能监控工具
echo - test_config.json: 测试配置文件
echo - 测试程序使用说明.md: 详细使用说明
echo.
echo 注意事项：
echo - 测试前确保服务器正常运行
echo - 大量客户端测试可能影响系统性能
echo - 建议先进行快速测试验证基本功能
echo.
pause
goto MENU

:EXIT
echo.
echo 感谢使用在线打字比赛系统测试程序！
echo.
exit /b 0
