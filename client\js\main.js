const nicknameGenerator = new NicknameGenerator();

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const nicknameInput = document.getElementById('nickname');
    const generateNicknameBtn = document.getElementById('generate-nickname');
    const startBtn = document.getElementById('start-btn');
    const loginScreen = document.getElementById('login-screen');
    const waitingScreen = document.getElementById('waiting-screen');
    
    // 生成昵称的函数
    function generateNewNickname() {
        const newNickname = nicknameGenerator.generate();
        nicknameInput.value = newNickname;
        return newNickname;
    }
    
    // 页面加载时立即生成一个默认昵称
    const defaultNickname = generateNewNickname();
    
    // 点击"换一个"按钮时生成新昵称
    generateNicknameBtn.addEventListener('click', generateNewNickname);
    
    // 开始按钮点击事件
    startBtn.addEventListener('click', function() {
        const currentNickname = nicknameInput.value;
        if (!currentNickname) {
            nicknameInput.value = generateNewNickname();
        }
        
        // 显示等待界面
        loginScreen.classList.add('d-none');
        waitingScreen.classList.remove('d-none');
        
        // 发送注册事件
        socket.emit('register_user', {
            nickname: nicknameInput.value
        });
    });
    
    // 确保昵称输入框始终有值
    nicknameInput.addEventListener('blur', function() {
        if (!this.value.trim()) {
            this.value = generateNewNickname();
        }
    });
    
    // 禁止用户手动编辑昵称
    nicknameInput.addEventListener('keydown', function(e) {
        e.preventDefault();
        return false;
    });

    // 添加到 DOMContentLoaded 事件处理中
    document.getElementById('restart-btn').addEventListener('click', function() {
        // 返回到等待界面
        document.getElementById('result-screen').classList.add('d-none');
        document.getElementById('waiting-screen').classList.remove('d-none');
        
        // 重新发送注册事件
        socket.emit('register_user', {
            nickname: document.getElementById('nickname').value
        });
    });
}); 