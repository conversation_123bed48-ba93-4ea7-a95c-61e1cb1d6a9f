#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在线打字比赛系统 - 多客户端并发测试程序

功能：
- 模拟多个虚拟客户端同时连接到服务器
- 测试服务器在高并发情况下的性能和稳定性
- 验证实时通信、排行榜更新等功能的正确性

使用方法：
1. 启动服务端（manager.py）
2. 运行此测试程序：python test_concurrent_clients.py
3. 在管理界面创建并开始比赛
4. 观察测试结果和性能报告

作者：AI Assistant
日期：2025-06-18
"""

import asyncio
import socketio
import random
import time
import threading
import argparse
import json
import psutil
import os
import sys
from datetime import datetime
from typing import List, Dict, Any
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_results.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ClientStats:
    """客户端统计信息"""
    client_id: int
    nickname: str
    connected: bool = False
    registered: bool = False
    contest_started: bool = False
    contest_completed: bool = False
    typing_speed: float = 0.0  # 字符/分钟
    error_rate: float = 0.0    # 错误率 (0-1)
    correct_count: int = 0
    error_count: int = 0
    final_score: int = 0
    connection_time: float = 0.0
    registration_time: float = 0.0
    completion_time: float = 0.0
    errors: List[str] = field(default_factory=list)

@dataclass
class TestResults:
    """测试结果统计"""
    total_clients: int = 0
    connected_clients: int = 0
    registered_clients: int = 0
    completed_clients: int = 0
    failed_connections: int = 0
    avg_connection_time: float = 0.0
    avg_registration_time: float = 0.0
    avg_completion_time: float = 0.0
    server_response_times: List[float] = field(default_factory=list)
    leaderboard_updates: int = 0
    errors: List[str] = field(default_factory=list)
    start_time: datetime = field(default_factory=datetime.now)
    end_time: datetime = None

class VirtualClient:
    """虚拟客户端类"""
    
    def __init__(self, client_id: int, server_url: str, stats: ClientStats):
        self.client_id = client_id
        self.server_url = server_url
        self.stats = stats
        self.sio = socketio.AsyncClient(
            reconnection=True,
            reconnection_attempts=3,
            reconnection_delay=1,
            reconnection_delay_max=5
        )
        self.contest_text = ""
        self.current_position = 0
        self.typing_task = None
        self.setup_event_handlers()
        
    def setup_event_handlers(self):
        """设置Socket.IO事件处理器"""
        
        @self.sio.event
        async def connect():
            logger.info(f"客户端 {self.client_id} 连接成功")
            self.stats.connected = True
            self.stats.connection_time = time.time()
            
            # 注册用户
            await self.register_user()
            
        @self.sio.event
        async def connect_error(data):
            error_msg = f"客户端 {self.client_id} 连接失败: {data}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            
        @self.sio.event
        async def disconnect():
            logger.info(f"客户端 {self.client_id} 断开连接")
            self.stats.connected = False
            
        @self.sio.event
        async def registration_success(data):
            logger.info(f"客户端 {self.client_id} 注册成功: {data['user']['nickname']}")
            self.stats.registered = True
            self.stats.registration_time = time.time()
            self.stats.nickname = data['user']['nickname']
            
            # 如果比赛已经开始，立即开始打字
            if data.get('contest') and data['contest'].get('status') == 'running':
                await self.start_contest(data['contest']['text_content'])
                
        @self.sio.event
        async def contest_status_update(data):
            status = data.get('status')
            logger.info(f"客户端 {self.client_id} 收到比赛状态更新: {status}")
            
            if status == 'running':
                # 获取比赛内容
                await self.fetch_contest_and_start()
            elif status == 'ended':
                logger.info(f"客户端 {self.client_id} 比赛结束")
                if self.typing_task:
                    self.typing_task.cancel()
                    
        @self.sio.event
        async def leaderboard_update(data):
            # 记录排行榜更新
            pass
            
        @self.sio.event
        async def user_count_update(data):
            # 记录用户数量更新
            pass
            
    async def connect(self):
        """连接到服务器"""
        try:
            start_time = time.time()
            await self.sio.connect(self.server_url)
            self.stats.connection_time = time.time() - start_time
            return True
        except Exception as e:
            error_msg = f"客户端 {self.client_id} 连接失败: {str(e)}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            return False
            
    async def register_user(self):
        """注册用户"""
        try:
            start_time = time.time()
            await self.sio.emit('register_user', {
                'nickname': self.stats.nickname
            })
            self.stats.registration_time = time.time() - start_time
        except Exception as e:
            error_msg = f"客户端 {self.client_id} 注册失败: {str(e)}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            
    async def fetch_contest_and_start(self):
        """获取比赛内容并开始打字"""
        try:
            # 这里应该通过HTTP API获取比赛内容
            # 为了简化，我们等待contest_status_update事件中的数据
            # 或者模拟一个标准的测试文本
            test_text = "hello world welcome to typing game good morning nice to meet you today is a beautiful day let us practice typing together"
            await self.start_contest(test_text)
        except Exception as e:
            error_msg = f"客户端 {self.client_id} 获取比赛内容失败: {str(e)}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            
    async def start_contest(self, text_content: str):
        """开始比赛"""
        if self.stats.contest_started:
            return
            
        logger.info(f"客户端 {self.client_id} 开始比赛")
        self.stats.contest_started = True
        self.contest_text = text_content
        self.current_position = 0
        
        # 启动打字模拟任务
        self.typing_task = asyncio.create_task(self.simulate_typing())
        
    async def simulate_typing(self):
        """模拟打字过程"""
        try:
            text_length = len(self.contest_text)
            chars_per_minute = self.stats.typing_speed
            chars_per_second = chars_per_minute / 60.0
            
            logger.info(f"客户端 {self.client_id} 开始模拟打字，速度: {chars_per_minute} 字符/分钟")
            
            while self.current_position < text_length:
                # 计算下一个字符的延迟
                delay = 1.0 / chars_per_second
                
                # 添加随机性，使打字更真实
                delay *= random.uniform(0.5, 1.5)
                
                await asyncio.sleep(delay)
                
                # 模拟打字错误
                if random.random() < self.stats.error_rate:
                    self.stats.error_count += 1
                else:
                    self.stats.correct_count += 1
                    
                self.current_position += 1
                
                # 每隔一段时间发送进度更新
                if self.current_position % 10 == 0 or self.current_position == text_length:
                    await self.send_typing_progress()
                    
            # 完成打字
            await self.complete_typing()
            
        except asyncio.CancelledError:
            logger.info(f"客户端 {self.client_id} 打字任务被取消")
        except Exception as e:
            error_msg = f"客户端 {self.client_id} 打字模拟失败: {str(e)}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            
    async def send_typing_progress(self):
        """发送打字进度"""
        try:
            completed = self.current_position >= len(self.contest_text)
            await self.sio.emit('typing_progress', {
                'correct_count': self.stats.correct_count,
                'error_count': self.stats.error_count,
                'completed': completed
            })
        except Exception as e:
            error_msg = f"客户端 {self.client_id} 发送进度失败: {str(e)}"
            logger.error(error_msg)
            self.stats.errors.append(error_msg)
            
    async def complete_typing(self):
        """完成打字"""
        logger.info(f"客户端 {self.client_id} 完成打字")
        self.stats.contest_completed = True
        self.stats.completion_time = time.time()
        
        # 计算最终得分
        base_score = self.stats.correct_count * 2
        penalty = self.stats.error_count * 1
        self.stats.final_score = max(0, base_score - penalty)
        
        # 发送最终进度
        await self.send_typing_progress()
        
    async def disconnect(self):
        """断开连接"""
        try:
            if self.typing_task:
                self.typing_task.cancel()
            await self.sio.disconnect()
        except Exception as e:
            logger.error(f"客户端 {self.client_id} 断开连接失败: {str(e)}")

class ConcurrentTestManager:
    """并发测试管理器"""
    
    def __init__(self, server_url: str = "http://localhost:5000", num_clients: int = 60):
        self.server_url = server_url
        self.num_clients = num_clients
        self.clients: List[VirtualClient] = []
        self.client_stats: List[ClientStats] = []
        self.test_results = TestResults(total_clients=num_clients)
        self.performance_monitor = None
        
    def generate_nickname(self) -> str:
        """生成随机太空主题昵称"""
        prefixes = ["星际", "宇宙", "银河", "太空", "星云", "彗星", "行星", "卫星", "火箭", "飞船"]
        suffixes = ["旅行者", "探索者", "战士", "飞行员", "指挥官", "科学家", "工程师", "猎人", "守护者", "先锋"]
        return f"{random.choice(prefixes)}{random.choice(suffixes)}{random.randint(1, 999)}"
        
    def create_clients(self):
        """创建虚拟客户端"""
        logger.info(f"创建 {self.num_clients} 个虚拟客户端...")
        
        for i in range(self.num_clients):
            # 生成客户端统计信息
            stats = ClientStats(
                client_id=i + 1,
                nickname=self.generate_nickname(),
                typing_speed=random.uniform(30, 80),  # 30-80 字符/分钟
                error_rate=random.uniform(0.05, 0.15)  # 5-15% 错误率
            )
            
            # 创建虚拟客户端
            client = VirtualClient(i + 1, self.server_url, stats)
            
            self.clients.append(client)
            self.client_stats.append(stats)
            
        logger.info(f"成功创建 {len(self.clients)} 个虚拟客户端")

    async def connect_all_clients(self):
        """连接所有客户端"""
        logger.info("开始连接所有客户端...")

        # 分批连接，避免同时连接过多客户端
        batch_size = 10
        for i in range(0, len(self.clients), batch_size):
            batch = self.clients[i:i + batch_size]

            # 并发连接当前批次的客户端
            tasks = [client.connect() for client in batch]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计连接结果
            for j, result in enumerate(results):
                if isinstance(result, Exception):
                    self.test_results.failed_connections += 1
                    logger.error(f"客户端 {batch[j].client_id} 连接失败: {result}")
                elif result:
                    self.test_results.connected_clients += 1

            # 批次间延迟
            if i + batch_size < len(self.clients):
                await asyncio.sleep(0.5)

        logger.info(f"连接完成: {self.test_results.connected_clients}/{self.num_clients} 成功")

    async def wait_for_contest_start(self):
        """等待比赛开始"""
        logger.info("等待管理员在服务端开始比赛...")
        logger.info("请在管理界面中：")
        logger.info("1. 设置比赛文本")
        logger.info("2. 点击'创建比赛'")
        logger.info("3. 点击'开始比赛'")

        # 等待至少一个客户端开始比赛
        while True:
            contest_started = any(stats.contest_started for stats in self.client_stats)
            if contest_started:
                logger.info("检测到比赛开始！")
                break
            await asyncio.sleep(1)

    async def wait_for_contest_completion(self):
        """等待比赛完成"""
        logger.info("等待所有客户端完成比赛...")

        start_time = time.time()
        timeout = 300  # 5分钟超时

        while time.time() - start_time < timeout:
            completed_count = sum(1 for stats in self.client_stats if stats.contest_completed)
            connected_count = sum(1 for stats in self.client_stats if stats.connected)

            if completed_count >= connected_count * 0.9:  # 90%的客户端完成即可
                logger.info(f"比赛完成: {completed_count}/{connected_count} 客户端完成")
                break

            await asyncio.sleep(2)

        self.test_results.completed_clients = sum(1 for stats in self.client_stats if stats.contest_completed)

    async def disconnect_all_clients(self):
        """断开所有客户端连接"""
        logger.info("断开所有客户端连接...")

        tasks = [client.disconnect() for client in self.clients]
        await asyncio.gather(*tasks, return_exceptions=True)

        logger.info("所有客户端已断开连接")

    def start_performance_monitoring(self):
        """开始性能监控"""
        def monitor():
            while True:
                try:
                    # 获取当前进程的CPU和内存使用情况
                    process = psutil.Process()
                    cpu_percent = process.cpu_percent()
                    memory_info = process.memory_info()

                    # 记录系统性能
                    system_cpu = psutil.cpu_percent()
                    system_memory = psutil.virtual_memory()

                    logger.debug(f"系统性能 - CPU: {system_cpu}%, 内存: {system_memory.percent}%")
                    logger.debug(f"进程性能 - CPU: {cpu_percent}%, 内存: {memory_info.rss / 1024 / 1024:.1f}MB")

                    time.sleep(5)
                except Exception as e:
                    logger.error(f"性能监控错误: {e}")
                    break

        self.performance_monitor = threading.Thread(target=monitor, daemon=True)
        self.performance_monitor.start()

    def calculate_statistics(self):
        """计算测试统计信息"""
        connected_stats = [stats for stats in self.client_stats if stats.connected]
        registered_stats = [stats for stats in self.client_stats if stats.registered]
        completed_stats = [stats for stats in self.client_stats if stats.contest_completed]

        self.test_results.registered_clients = len(registered_stats)
        self.test_results.completed_clients = len(completed_stats)

        # 计算平均时间
        if connected_stats:
            self.test_results.avg_connection_time = sum(s.connection_time for s in connected_stats) / len(connected_stats)

        if registered_stats:
            self.test_results.avg_registration_time = sum(s.registration_time for s in registered_stats) / len(registered_stats)

        if completed_stats:
            self.test_results.avg_completion_time = sum(s.completion_time for s in completed_stats) / len(completed_stats)

        self.test_results.end_time = datetime.now()

    def generate_report(self):
        """生成测试报告"""
        report = []
        report.append("=" * 80)
        report.append("在线打字比赛系统 - 并发测试报告")
        report.append("=" * 80)
        report.append(f"测试时间: {self.test_results.start_time.strftime('%Y-%m-%d %H:%M:%S')} - {self.test_results.end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"测试持续时间: {(self.test_results.end_time - self.test_results.start_time).total_seconds():.2f} 秒")
        report.append("")

        # 连接统计
        report.append("连接统计:")
        report.append(f"  总客户端数: {self.test_results.total_clients}")
        report.append(f"  成功连接: {self.test_results.connected_clients}")
        report.append(f"  连接失败: {self.test_results.failed_connections}")
        report.append(f"  连接成功率: {(self.test_results.connected_clients / self.test_results.total_clients * 100):.2f}%")
        report.append(f"  平均连接时间: {self.test_results.avg_connection_time:.3f} 秒")
        report.append("")

        # 注册统计
        report.append("注册统计:")
        report.append(f"  成功注册: {self.test_results.registered_clients}")
        report.append(f"  注册成功率: {(self.test_results.registered_clients / max(1, self.test_results.connected_clients) * 100):.2f}%")
        report.append(f"  平均注册时间: {self.test_results.avg_registration_time:.3f} 秒")
        report.append("")

        # 比赛统计
        report.append("比赛统计:")
        report.append(f"  完成比赛: {self.test_results.completed_clients}")
        report.append(f"  完成率: {(self.test_results.completed_clients / max(1, self.test_results.registered_clients) * 100):.2f}%")

        if self.test_results.completed_clients > 0:
            completed_stats = [stats for stats in self.client_stats if stats.contest_completed]
            avg_correct = sum(s.correct_count for s in completed_stats) / len(completed_stats)
            avg_errors = sum(s.error_count for s in completed_stats) / len(completed_stats)
            avg_score = sum(s.final_score for s in completed_stats) / len(completed_stats)
            avg_speed = sum(s.typing_speed for s in completed_stats) / len(completed_stats)

            report.append(f"  平均正确字符数: {avg_correct:.1f}")
            report.append(f"  平均错误字符数: {avg_errors:.1f}")
            report.append(f"  平均得分: {avg_score:.1f}")
            report.append(f"  平均打字速度: {avg_speed:.1f} 字符/分钟")
        report.append("")

        # 错误统计
        all_errors = []
        for stats in self.client_stats:
            all_errors.extend(stats.errors)
        all_errors.extend(self.test_results.errors)

        if all_errors:
            report.append("错误统计:")
            report.append(f"  总错误数: {len(all_errors)}")
            report.append("  错误详情:")
            for error in all_errors[:10]:  # 只显示前10个错误
                report.append(f"    - {error}")
            if len(all_errors) > 10:
                report.append(f"    ... 还有 {len(all_errors) - 10} 个错误")
        else:
            report.append("错误统计: 无错误")

        report.append("")
        report.append("=" * 80)

        # 输出报告
        report_text = "\n".join(report)
        print(report_text)

        # 保存报告到文件
        with open(f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt", 'w', encoding='utf-8') as f:
            f.write(report_text)

        return report_text

    async def run_test(self):
        """运行完整的测试流程"""
        try:
            logger.info("开始并发测试...")
            self.test_results.start_time = datetime.now()

            # 开始性能监控
            self.start_performance_monitoring()

            # 创建客户端
            self.create_clients()

            # 连接所有客户端
            await self.connect_all_clients()

            # 等待比赛开始
            await self.wait_for_contest_start()

            # 等待比赛完成
            await self.wait_for_contest_completion()

            # 等待一段时间确保所有数据传输完成
            await asyncio.sleep(5)

        except KeyboardInterrupt:
            logger.info("测试被用户中断")
        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
            self.test_results.errors.append(str(e))
        finally:
            # 断开所有连接
            await self.disconnect_all_clients()

            # 计算统计信息
            self.calculate_statistics()

            # 生成报告
            self.generate_report()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='在线打字比赛系统 - 多客户端并发测试')
    parser.add_argument('--server', '-s', default='http://localhost:5000',
                       help='服务器地址 (默认: http://localhost:5000)')
    parser.add_argument('--clients', '-c', type=int, default=60,
                       help='客户端数量 (默认: 60)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    print("=" * 80)
    print("在线打字比赛系统 - 多客户端并发测试程序")
    print("=" * 80)
    print(f"服务器地址: {args.server}")
    print(f"客户端数量: {args.clients}")
    print()
    print("使用说明:")
    print("1. 确保服务端已启动 (运行 manager.py)")
    print("2. 本程序将自动连接虚拟客户端")
    print("3. 请在管理界面中创建并开始比赛")
    print("4. 程序将自动模拟所有客户端的打字过程")
    print("5. 测试完成后将生成详细报告")
    print()
    print("按 Ctrl+C 可随时停止测试")
    print("=" * 80)

    # 创建测试管理器
    test_manager = ConcurrentTestManager(args.server, args.clients)

    # 运行测试
    try:
        asyncio.run(test_manager.run_test())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试失败: {e}")
        logger.error(f"测试失败: {e}")

    print("\n测试完成！")

if __name__ == "__main__":
    main()
