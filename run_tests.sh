#!/bin/bash

# 在线打字比赛系统 - 自动化测试程序 (Linux/macOS)

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示标题
show_title() {
    echo -e "${BLUE}================================================================${NC}"
    echo -e "${BLUE}在线打字比赛系统 - 自动化测试程序${NC}"
    echo -e "${BLUE}================================================================${NC}"
    echo
}

# 显示菜单
show_menu() {
    echo "请选择测试类型："
    echo
    echo "1. 快速测试 (10个客户端)"
    echo "2. 标准测试 (30个客户端)"
    echo "3. 压力测试 (60个客户端)"
    echo "4. 自定义测试"
    echo "5. 性能监控"
    echo "6. 查看帮助"
    echo "7. 退出"
    echo
}

# 检查Python和依赖
check_dependencies() {
    echo -e "${YELLOW}检查依赖...${NC}"
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}错误: 未找到Python3${NC}"
        echo "请安装Python 3.7或更高版本"
        exit 1
    fi
    
    # 检查依赖包
    python3 -c "import socketio, psutil, requests" 2>/dev/null
    if [ $? -ne 0 ]; then
        echo -e "${YELLOW}警告: 缺少必要的Python包${NC}"
        echo "请运行: pip3 install python-socketio[asyncio] psutil requests"
        read -p "是否继续？(y/n): " continue_anyway
        if [[ $continue_anyway != "y" && $continue_anyway != "Y" ]]; then
            exit 1
        fi
    fi
    
    echo -e "${GREEN}依赖检查完成${NC}"
    echo
}

# 快速测试
quick_test() {
    echo
    echo -e "${GREEN}开始快速测试...${NC}"
    echo "================================================================"
    python3 quick_test.py --clients 10
    echo
    echo -e "${GREEN}快速测试完成！${NC}"
    read -p "按回车键继续..."
}

# 标准测试
standard_test() {
    echo
    echo -e "${GREEN}开始标准测试...${NC}"
    echo "================================================================"
    python3 test_concurrent_clients.py --clients 30
    echo
    echo -e "${GREEN}标准测试完成！${NC}"
    read -p "按回车键继续..."
}

# 压力测试
stress_test() {
    echo
    echo -e "${YELLOW}开始压力测试...${NC}"
    echo "================================================================"
    echo -e "${RED}警告: 压力测试将使用60个客户端，可能对系统造成较大负载。${NC}"
    read -p "确认继续？(y/n): " confirm
    if [[ $confirm == "y" || $confirm == "Y" ]]; then
        python3 test_concurrent_clients.py --clients 60 --verbose
    else
        echo "测试已取消。"
    fi
    echo
    echo -e "${GREEN}压力测试完成！${NC}"
    read -p "按回车键继续..."
}

# 自定义测试
custom_test() {
    echo
    echo "自定义测试设置"
    echo "================================================================"
    
    read -p "服务器地址 (默认: http://localhost:5000): " server
    server=${server:-http://localhost:5000}
    
    read -p "客户端数量 (默认: 20): " clients
    clients=${clients:-20}
    
    read -p "详细输出？(y/n, 默认: n): " verbose
    if [[ $verbose == "y" || $verbose == "Y" ]]; then
        verbose_flag="--verbose"
    else
        verbose_flag=""
    fi
    
    echo
    echo -e "${GREEN}开始自定义测试...${NC}"
    echo "服务器: $server"
    echo "客户端数量: $clients"
    echo "================================================================"
    python3 test_concurrent_clients.py --server "$server" --clients "$clients" $verbose_flag
    echo
    echo -e "${GREEN}自定义测试完成！${NC}"
    read -p "按回车键继续..."
}

# 性能监控
performance_monitor() {
    echo
    echo "性能监控设置"
    echo "================================================================"
    
    read -p "监控间隔秒数 (默认: 5): " interval
    interval=${interval:-5}
    
    read -p "输出文件名 (默认: 不保存): " output
    if [[ -n "$output" ]]; then
        output_flag="--output $output"
    else
        output_flag=""
    fi
    
    read -p "服务器地址 (默认: http://localhost:5000): " server
    server=${server:-http://localhost:5000}
    
    echo
    echo -e "${GREEN}开始性能监控...${NC}"
    echo "监控间隔: $interval 秒"
    echo "服务器: $server"
    if [[ -n "$output" ]]; then
        echo "输出文件: $output"
    fi
    echo "按 Ctrl+C 停止监控"
    echo "================================================================"
    python3 performance_monitor.py --interval "$interval" --server "$server" $output_flag
    echo
    echo -e "${GREEN}性能监控完成！${NC}"
    read -p "按回车键继续..."
}

# 显示帮助
show_help() {
    echo
    echo "帮助信息"
    echo "================================================================"
    echo
    echo "使用前准备："
    echo "1. 确保已安装Python 3.7+"
    echo "2. 安装依赖：pip3 install python-socketio[asyncio] psutil requests"
    echo "3. 启动服务端：python3 server/manager.py"
    echo "4. 在管理界面点击\"启动服务器\""
    echo
    echo "测试流程："
    echo "1. 选择并运行测试程序"
    echo "2. 等待客户端连接完成"
    echo "3. 在管理界面设置比赛文本"
    echo "4. 点击\"创建比赛\"然后\"开始比赛\""
    echo "5. 观察测试过程和结果"
    echo
    echo "文件说明："
    echo "- test_concurrent_clients.py: 完整并发测试"
    echo "- quick_test.py: 快速测试工具"
    echo "- performance_monitor.py: 性能监控工具"
    echo "- test_config.json: 测试配置文件"
    echo "- 测试程序使用说明.md: 详细使用说明"
    echo
    echo "注意事项："
    echo "- 测试前确保服务器正常运行"
    echo "- 大量客户端测试可能影响系统性能"
    echo "- 建议先进行快速测试验证基本功能"
    echo
    read -p "按回车键继续..."
}

# 主循环
main() {
    show_title
    check_dependencies
    
    while true; do
        show_menu
        read -p "请输入选项 (1-7): " choice
        
        case $choice in
            1)
                quick_test
                ;;
            2)
                standard_test
                ;;
            3)
                stress_test
                ;;
            4)
                custom_test
                ;;
            5)
                performance_monitor
                ;;
            6)
                show_help
                ;;
            7)
                echo
                echo -e "${GREEN}感谢使用在线打字比赛系统测试程序！${NC}"
                echo
                exit 0
                ;;
            *)
                echo -e "${RED}无效选项，请重新选择。${NC}"
                echo
                ;;
        esac
    done
}

# 运行主程序
main
