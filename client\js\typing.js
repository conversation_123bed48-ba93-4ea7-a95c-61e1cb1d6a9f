class TypingGame {
    constructor() {
        this.textDisplay = document.getElementById('text-display');
        this.typingInput = document.getElementById('typing-input');
        this.correctCount = document.getElementById('correct-count');
        this.errorCount = document.getElementById('error-count');
        this.accuracyDisplay = document.getElementById('accuracy');
        this.scoreDisplay = document.getElementById('score');

        this.originalText = '';
        this.currentIndex = 0;
        this.errors = 0;
        this.corrects = 0;
        this.currentLineIndex = 0;
        this.lines = [];
        this.currentPage = 0;
        this.linesPerPage = 3; // 每页显示3行

        // 添加累计分数的变量
        this.totalCorrects = 0;  // 总正确数
        this.totalErrors = 0;    // 总错误数
        this.currentLineCorrects = 0;  // 当前行正确数
        this.currentLineErrors = 0;    // 当前行错误数

        this.setupEventListeners();
        this.preventCopyPaste();
    }

    setupEventListeners() {
        this.typingInput.addEventListener('input', this.handleInput.bind(this));

        // 添加键盘事件监听，处理特殊按键
        this.typingInput.addEventListener('keydown', this.handleKeyDown.bind(this));

        // 添加虚拟键盘功能
        this.setupVirtualKeyboard();
    }

    // 处理键盘按键事件
    handleKeyDown(event) {
        // 允许用户按Enter键手动跳转到下一行
        if (event.key === 'Enter') {
            event.preventDefault(); // 阻止默认行为

            const inputText = this.typingInput.value;
            const currentLine = this.lines[this.currentLineIndex];

            // 如果当前行已经输入了一定数量的字符，允许跳转
            if (currentLine && inputText.length > 0) {
                // 将当前行未输入的字符算作错误
                this.currentLineErrors += (currentLine.length - inputText.length);

                // 跳转到下一行
                this.moveToNextLine();
            }
        }
    }

    preventCopyPaste() {
        // 禁止复制粘贴
        this.typingInput.addEventListener('copy', e => e.preventDefault());
        this.typingInput.addEventListener('paste', e => e.preventDefault());
        this.typingInput.addEventListener('cut', e => e.preventDefault());

        // 禁止右键菜单
        this.typingInput.addEventListener('contextmenu', e => e.preventDefault());

        // 禁止快捷键
        this.typingInput.addEventListener('keydown', e => {
            if ((e.ctrlKey || e.metaKey) &&
                (e.key === 'c' || e.key === 'v' || e.key === 'x')) {
                e.preventDefault();
            }
        });

        // 禁止拖放操作
        this.typingInput.addEventListener('dragover', e => e.preventDefault());
        this.typingInput.addEventListener('dragenter', e => e.preventDefault());
        this.typingInput.addEventListener('dragleave', e => e.preventDefault());
        this.typingInput.addEventListener('drop', e => e.preventDefault());
    }

    setupVirtualKeyboard() {
        const keys = document.querySelectorAll('.key');

        // 只保留键盘按下时的发光效果
        document.addEventListener('keydown', function(e) {
            const key = e.key.toLowerCase();
            const keyElement = document.querySelector(`.key[data-key="${key}"]`);

            if (keyElement) {
                keyElement.classList.add('active', 'glow');
                setTimeout(() => {
                    keyElement.classList.remove('active', 'glow');
                }, 300);
            } else if (key === ' ' || key === 'space') {
                const spaceKey = document.querySelector('.key.space');
                if (spaceKey) {
                    spaceKey.classList.add('active', 'glow');
                    setTimeout(() => {
                        spaceKey.classList.remove('active', 'glow');
                    }, 300);
                }
            }
        });
    }

    // 检测文本是否主要是中文
    isMostlyChineseText(text) {
        if (!text) return false;

        // 计算中文字符数量
        let chineseCount = 0;
        for (let i = 0; i < text.length; i++) {
            const charCode = text.charCodeAt(i);
            // 检测是否是中文字符范围
            if ((charCode >= 0x4e00 && charCode <= 0x9fff) || // CJK统一表意文字
                (charCode >= 0x3400 && charCode <= 0x4dbf) || // CJK扩展A
                (charCode >= 0x20000 && charCode <= 0x2a6df) || // CJK扩展B
                (charCode >= 0x2a700 && charCode <= 0x2b73f) || // CJK扩展C
                (charCode >= 0x2b740 && charCode <= 0x2b81f) || // CJK扩展D
                (charCode >= 0x2b820 && charCode <= 0x2ceaf) || // CJK扩展E
                (charCode >= 0xf900 && charCode <= 0xfaff) || // CJK兼容形式
                (charCode >= 0x3300 && charCode <= 0x33ff) || // CJK符号和标点
                (charCode >= 0xfe30 && charCode <= 0xfe4f) || // CJK兼容形式
                (charCode >= 0xff00 && charCode <= 0xffef)) { // 半形和全形字符
                chineseCount++;
            }
        }

        // 如果中文字符数量超过总字符数的三分之一，则认为是中文文本
        return chineseCount / text.length > 0.3;
    }

    // 获取字符的显示宽度（中文字符通常是英文字符的两倍宽）
    getCharDisplayWidth(char) {
        const charCode = char.charCodeAt(0);
        // 检测是否是中文字符或全角字符
        if ((charCode >= 0x4e00 && charCode <= 0x9fff) || // CJK统一表意文字
            (charCode >= 0x3400 && charCode <= 0x4dbf) || // CJK扩展A
            (charCode >= 0x20000 && charCode <= 0x2a6df) || // CJK扩展B
            (charCode >= 0x2a700 && charCode <= 0x2b73f) || // CJK扩展C
            (charCode >= 0x2b740 && charCode <= 0x2b81f) || // CJK扩展D
            (charCode >= 0x2b820 && charCode <= 0x2ceaf) || // CJK扩展E
            (charCode >= 0xf900 && charCode <= 0xfaff) || // CJK兼容形式
            (charCode >= 0xff00 && charCode <= 0xffef)) { // 全角字符
            return 2;
        }
        return 1;
    }

    start(text) {
        // 保存原始文本
        this.originalText = text;
        this.lines = [];

        // 检测文本是否主要是中文
        const isMostlyChinese = this.isMostlyChineseText(text);

        // 优化的文本分割逻辑，更好地处理中英文混合文本
        if (isMostlyChinese) {
            // 中文文本处理：考虑字符的显示宽度
            const maxWidthPerLine = 40; // 每行最大显示宽度
            let currentLine = '';
            let currentWidth = 0;

            for (let i = 0; i < text.length; i++) {
                const char = text[i];
                const charWidth = this.getCharDisplayWidth(char);

                // 如果添加当前字符会超出行宽度，则开始新行
                if (currentWidth + charWidth > maxWidthPerLine) {
                    this.lines.push(currentLine);
                    currentLine = char;
                    currentWidth = charWidth;
                } else {
                    currentLine += char;
                    currentWidth += charWidth;
                }

                // 如果遇到标点符号且不在行首，考虑换行
                if (i < text.length - 1 &&
                    /[，。！？；：,.!?;:]/.test(char) &&
                    currentWidth > maxWidthPerLine * 0.8) {
                    this.lines.push(currentLine);
                    currentLine = '';
                    currentWidth = 0;
                }
            }

            // 添加最后一行
            if (currentLine) {
                this.lines.push(currentLine);
            }
        } else {
            // 英文文本处理：按单词分割
            const words = text.split(/\s+/);
            let currentLine = [];
            let currentLength = 0;

            for (let word of words) {
                // 检查单词中是否包含中文字符
                let wordWidth = 0;
                for (let i = 0; i < word.length; i++) {
                    wordWidth += this.getCharDisplayWidth(word[i]);
                }

                if (currentLength + wordWidth + (currentLength > 0 ? 1 : 0) > 40) {
                    this.lines.push(currentLine.join(' '));
                    currentLine = [word];
                    currentLength = wordWidth;
                } else {
                    if (currentLength > 0) currentLength += 1;
                    currentLength += wordWidth;
                    currentLine.push(word);
                }
            }
            if (currentLine.length > 0) {
                this.lines.push(currentLine.join(' '));
            }
        }

        this.currentLineIndex = 0;
        this.currentPage = 0;
        this.errors = 0;
        this.corrects = 0;
        this.updateStats();
        this.renderText('');
        this.typingInput.value = '';
        this.typingInput.disabled = false;
        this.typingInput.focus();

        document.getElementById('result-screen').classList.add('d-none');
    }

    handleInput(event) {
        const inputText = this.typingInput.value;
        const currentLine = this.lines[this.currentLineIndex];

        // 如果当前行不存在，可能是数组越界，直接返回
        if (!currentLine) {
            console.error('当前行不存在，行索引：', this.currentLineIndex);
            return;
        }

        // 更新当前行的正确和错误计数
        this.currentLineCorrects = 0;
        this.currentLineErrors = 0;

        // 检查每个字符，优化中英文混合文本的比较
        for (let i = 0; i < inputText.length; i++) {
            if (i >= currentLine.length) {
                // 输入超出当前行长度
                this.currentLineErrors++;
            } else {
                // 规范化比较，处理全角半角字符差异
                const inputChar = inputText[i];
                const expectedChar = currentLine[i];

                // 直接比较字符
                if (inputChar === expectedChar) {
                    this.currentLineCorrects++;
                } else {
                    // 处理全角半角空格的特殊情况
                    if ((inputChar === ' ' && expectedChar === '　') ||
                        (inputChar === '　' && expectedChar === ' ')) {
                        this.currentLineCorrects++;
                    } else {
                        this.currentLineErrors++;
                    }
                }
            }
        }

        // 更新总计数（用于发送到服务器）
        this.corrects = this.totalCorrects + this.currentLineCorrects;
        this.errors = this.totalErrors + this.currentLineErrors;

        this.renderText(inputText);
        this.updateStats();

        // 发送进度到服务器
        socket.emit('typing_progress', {
            correct_count: this.corrects,
            error_count: this.errors,
            completed: this.currentLineIndex >= this.lines.length - 1 &&
                      inputText.length >= currentLine.length
        });

        // 判断是否完成当前行的输入
        // 当输入的字符数量等于当前行的长度时，自动跳转到下一行
        if (inputText.length === currentLine.length) {
            this.moveToNextLine();
        }
    }

    // 新增方法：移动到下一行
    moveToNextLine() {
        const currentLine = this.lines[this.currentLineIndex];
        if (!currentLine) return;

        // 累加当前行的统计到总数中
        this.totalCorrects += this.currentLineCorrects;
        this.totalErrors += this.currentLineErrors;

        if (this.currentLineIndex < this.lines.length - 1) {
            // 进入下一行
            this.currentLineIndex++;

            // 检查是否需要翻页
            const nextPage = Math.floor(this.currentLineIndex / this.linesPerPage);
            if (nextPage > this.currentPage) {
                this.currentPage = nextPage;
                showNotification('进入下一页');
            }

            // 清空输入框并更新显示
            this.typingInput.value = '';
            this.renderText('');

            // 计算当前页所有行的总字符数
            const startLine = this.currentPage * this.linesPerPage;
            const endLine = Math.min(startLine + this.linesPerPage, this.lines.length);
            let totalChars = 0;
            for (let i = startLine; i < endLine; i++) {
                if (this.lines[i]) {
                    totalChars += this.lines[i].length;
                }
            }

            // 设置输入框的最大长度
            this.typingInput.maxLength = totalChars;

            // 确保输入框获得焦点
            this.typingInput.focus();

            // 添加日志信息便于调试
            console.log(`移动到下一行: ${this.currentLineIndex}, 页码: ${this.currentPage}`);
        } else {
            // 完成所有行
            this.complete();
        }
    }



    renderText(inputText) {
        const currentLine = this.lines[this.currentLineIndex];
        let html = '';

        // 如果当前行不存在，可能是数组越界，直接返回
        if (!currentLine) {
            console.error('当前行不存在，行索引：', this.currentLineIndex);
            return;
        }

        // 计算当前页的行范围
        const startLine = Math.floor(this.currentLineIndex / this.linesPerPage) * this.linesPerPage;
        const endLine = Math.min(startLine + this.linesPerPage, this.lines.length);

        // 显示当前页的所有行
        for (let i = startLine; i < endLine; i++) {
            const line = this.lines[i];
            if (!line) continue; // 跳过不存在的行

            if (i === this.currentLineIndex) {
                // 当前输入行
                html += '<div class="current-line">';
                for (let j = 0; j < line.length; j++) {
                    const char = line[j];
                    if (j < inputText.length) {
                        const inputChar = inputText[j];
                        // 优化字符比较，处理全角半角差异
                        if (inputChar === char ||
                            (inputChar === ' ' && char === '　') ||
                            (inputChar === '　' && char === ' ')) {
                            html += `<span style="color: #3498db">${this.escapeHtml(char)}</span>`;
                        } else {
                            html += `<span class="incorrect">${this.escapeHtml(char)}</span>`;
                        }
                    } else {
                        html += this.escapeHtml(char);
                    }
                }
                html += '</div>';
            } else {
                // 其他行
                html += `<div class="other-line">${this.escapeHtml(line)}</div>`;
            }
        }

        // 添加页码信息
        const totalPages = Math.ceil(this.lines.length / this.linesPerPage);
        const currentPage = Math.floor(this.currentLineIndex / this.linesPerPage) + 1;
        html += `<div class="page-info">第 ${currentPage}/${totalPages} 页</div>`;

        this.textDisplay.innerHTML = html;

        // 确保当前行可见
        const currentLineElement = this.textDisplay.querySelector('.current-line');
        if (currentLineElement) {
            currentLineElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    // 转义HTML特殊字符，防止XSS攻击和显示问题
    escapeHtml(text) {
        if (!text) return '';
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }

    updateStats() {
        // 使用累计的总数来计算统计信息
        const total = this.corrects + this.errors;
        const accuracy = total > 0 ? (this.corrects / total * 100) : 0;

        // 使用累计的正确数计算得分，每个错误扣1分，但最低为0分
        const baseScore = this.corrects * 2;  // 每个正确字符得2分
        const penalty = this.errors * 1;      // 每个错误字符扣1分
        const score = Math.max(0, baseScore - penalty);  // 确保分数不低于0

        this.correctCount.textContent = this.corrects;
        this.errorCount.textContent = this.errors;
        this.accuracyDisplay.textContent = accuracy.toFixed(1) + '%';
        this.scoreDisplay.textContent = score;
    }

    complete() {
        this.typingInput.disabled = true;

        // 计算得分但不显示最终结果
        this.calculateScore();

        // 发送完成状态到服务器
        socket.emit('typing_progress', {
            correct_count: this.corrects,
            error_count: this.errors,
            completed: true
        });

        // 显示等待比赛结束的消息
        this.textDisplay.innerHTML = `<div class="completed-message">输入完成！等待比赛结束...</div>`;

        // 隐藏输入框，显示等待提示
        this.typingInput.classList.add('d-none');
        const waitingMsg = document.createElement('div');
        waitingMsg.className = 'waiting-message alert alert-info';
        waitingMsg.innerHTML = '恭喜完成！等待其他宇航员完成挑战...<div class="spinner-border spinner-border-sm" role="status"></div>';
        this.typingInput.parentNode.appendChild(waitingMsg);
    }

    calculateScore() {
        // 计算最终得分，考虑扣分
        const baseScore = this.corrects * 2;      // 每个正确字符得2分
        const penalty = this.errors * 1;          // 每个错误字符扣1分
        const finalScore = Math.max(0, baseScore - penalty);  // 确保分数不低于0

        // 更新结果界面
        document.getElementById('final-nickname').textContent = document.getElementById('user-nickname').textContent;
        document.getElementById('final-score').textContent = finalScore;

        // 更新结果界面的排行榜
        socket.emit('request_leaderboard');

        showNotification('恭喜完成！');
    }
}

// 创建全局实例
window.typingGame = new TypingGame();

// 修改 showTypingScreen 函数
window.showTypingScreen = function(text) {
    document.getElementById('waiting-screen').classList.add('d-none');
    document.getElementById('result-screen').classList.add('d-none');
    document.getElementById('typing-screen').classList.remove('d-none');

    // 使用 TypingGame 实例启动游戏
    window.typingGame.start(text);

    showNotification('比赛开始！');
}