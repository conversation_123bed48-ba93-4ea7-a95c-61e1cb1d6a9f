#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在线打字比赛系统 - 性能监控工具

用于监控服务器性能指标，包括CPU、内存、网络连接等。

使用方法：
python performance_monitor.py [--interval 5] [--output monitor.log]

作者：AI Assistant
日期：2025-06-18
"""

import psutil
import time
import argparse
import json
import threading
from datetime import datetime
from typing import Dict, List
import requests

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, interval: int = 5, output_file: str = None, server_url: str = None):
        self.interval = interval
        self.output_file = output_file
        self.server_url = server_url
        self.monitoring = False
        self.data_points: List[Dict] = []
        
    def get_system_info(self) -> Dict:
        """获取系统信息"""
        try:
            # CPU信息
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # 内存信息
            memory = psutil.virtual_memory()
            
            # 磁盘信息
            disk = psutil.disk_usage('/')
            
            # 网络信息
            network = psutil.net_io_counters()
            
            # 进程信息
            processes = len(psutil.pids())
            
            return {
                'timestamp': datetime.now().isoformat(),
                'cpu': {
                    'percent': cpu_percent,
                    'count': cpu_count
                },
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'percent': memory.percent,
                    'used': memory.used
                },
                'disk': {
                    'total': disk.total,
                    'used': disk.used,
                    'free': disk.free,
                    'percent': (disk.used / disk.total) * 100
                },
                'network': {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                },
                'processes': processes
            }
        except Exception as e:
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}
            
    def get_server_info(self) -> Dict:
        """获取服务器信息"""
        if not self.server_url:
            return {}
            
        try:
            # 尝试获取在线用户数
            response = requests.get(f"{self.server_url}/api/users/online", timeout=5)
            if response.status_code == 200:
                users = response.json()
                return {
                    'online_users': len(users),
                    'server_responsive': True
                }
            else:
                return {
                    'online_users': 0,
                    'server_responsive': False,
                    'status_code': response.status_code
                }
        except Exception as e:
            return {
                'online_users': 0,
                'server_responsive': False,
                'error': str(e)
            }
            
    def get_python_process_info(self) -> Dict:
        """获取Python进程信息"""
        try:
            python_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_info']):
                try:
                    if 'python' in proc.info['name'].lower():
                        python_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cpu_percent': proc.info['cpu_percent'],
                            'memory_mb': proc.info['memory_info'].rss / 1024 / 1024
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
            return {'python_processes': python_processes}
        except Exception as e:
            return {'python_processes_error': str(e)}
            
    def collect_data(self) -> Dict:
        """收集所有监控数据"""
        data = {}
        
        # 系统信息
        data.update(self.get_system_info())
        
        # 服务器信息
        server_info = self.get_server_info()
        if server_info:
            data['server'] = server_info
            
        # Python进程信息
        python_info = self.get_python_process_info()
        data.update(python_info)
        
        return data
        
    def format_bytes(self, bytes_value: int) -> str:
        """格式化字节数"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.2f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.2f} PB"
        
    def print_data(self, data: Dict):
        """打印监控数据"""
        timestamp = data.get('timestamp', 'Unknown')
        print(f"\n[{timestamp}]")
        print("-" * 60)
        
        # CPU和内存
        if 'cpu' in data:
            print(f"CPU: {data['cpu']['percent']:.1f}% ({data['cpu']['count']} cores)")
            
        if 'memory' in data:
            mem = data['memory']
            print(f"内存: {mem['percent']:.1f}% ({self.format_bytes(mem['used'])}/{self.format_bytes(mem['total'])})")
            
        # 磁盘
        if 'disk' in data:
            disk = data['disk']
            print(f"磁盘: {disk['percent']:.1f}% ({self.format_bytes(disk['used'])}/{self.format_bytes(disk['total'])})")
            
        # 服务器信息
        if 'server' in data:
            server = data['server']
            status = "响应" if server.get('server_responsive') else "无响应"
            users = server.get('online_users', 0)
            print(f"服务器: {status}, 在线用户: {users}")
            
        # Python进程
        if 'python_processes' in data:
            processes = data['python_processes']
            if processes:
                print(f"Python进程: {len(processes)} 个")
                for proc in processes[:3]:  # 只显示前3个
                    print(f"  PID {proc['pid']}: {proc['memory_mb']:.1f}MB, CPU {proc['cpu_percent']:.1f}%")
                    
        # 进程总数
        if 'processes' in data:
            print(f"系统进程总数: {data['processes']}")
            
    def save_data(self, data: Dict):
        """保存数据到文件"""
        if not self.output_file:
            return
            
        try:
            with open(self.output_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(data, ensure_ascii=False) + '\n')
        except Exception as e:
            print(f"保存数据失败: {e}")
            
    def start_monitoring(self):
        """开始监控"""
        print("开始性能监控...")
        print(f"监控间隔: {self.interval} 秒")
        if self.output_file:
            print(f"输出文件: {self.output_file}")
        if self.server_url:
            print(f"服务器地址: {self.server_url}")
        print("按 Ctrl+C 停止监控")
        print("=" * 60)
        
        self.monitoring = True
        
        try:
            while self.monitoring:
                # 收集数据
                data = self.collect_data()
                self.data_points.append(data)
                
                # 显示数据
                self.print_data(data)
                
                # 保存数据
                self.save_data(data)
                
                # 等待下一次监控
                time.sleep(self.interval)
                
        except KeyboardInterrupt:
            print("\n监控被用户停止")
        except Exception as e:
            print(f"\n监控过程中发生错误: {e}")
        finally:
            self.monitoring = False
            self.generate_summary()
            
    def generate_summary(self):
        """生成监控摘要"""
        if not self.data_points:
            print("没有收集到监控数据")
            return
            
        print("\n" + "=" * 60)
        print("监控摘要")
        print("=" * 60)
        
        # 计算平均值
        cpu_values = [d.get('cpu', {}).get('percent', 0) for d in self.data_points if 'cpu' in d]
        memory_values = [d.get('memory', {}).get('percent', 0) for d in self.data_points if 'memory' in d]
        
        if cpu_values:
            print(f"CPU使用率: 平均 {sum(cpu_values)/len(cpu_values):.1f}%, 最高 {max(cpu_values):.1f}%")
            
        if memory_values:
            print(f"内存使用率: 平均 {sum(memory_values)/len(memory_values):.1f}%, 最高 {max(memory_values):.1f}%")
            
        # 服务器响应性
        server_responsive_count = sum(1 for d in self.data_points 
                                    if d.get('server', {}).get('server_responsive', False))
        if server_responsive_count > 0:
            response_rate = (server_responsive_count / len(self.data_points)) * 100
            print(f"服务器响应率: {response_rate:.1f}%")
            
        # 在线用户数
        user_counts = [d.get('server', {}).get('online_users', 0) for d in self.data_points 
                      if 'server' in d]
        if user_counts:
            print(f"在线用户数: 平均 {sum(user_counts)/len(user_counts):.1f}, 最高 {max(user_counts)}")
            
        print(f"监控时长: {len(self.data_points) * self.interval} 秒")
        print(f"数据点数: {len(self.data_points)}")
        
        if self.output_file:
            print(f"详细数据已保存到: {self.output_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='在线打字比赛系统 - 性能监控工具')
    parser.add_argument('--interval', '-i', type=int, default=5,
                       help='监控间隔（秒）')
    parser.add_argument('--output', '-o', type=str,
                       help='输出文件路径')
    parser.add_argument('--server', '-s', type=str, default='http://localhost:5000',
                       help='服务器地址')
    
    args = parser.parse_args()
    
    # 创建监控器
    monitor = PerformanceMonitor(
        interval=args.interval,
        output_file=args.output,
        server_url=args.server
    )
    
    # 开始监控
    monitor.start_monitoring()

if __name__ == "__main__":
    main()
