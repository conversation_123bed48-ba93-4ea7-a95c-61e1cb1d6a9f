#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在线打字比赛系统 - 快速测试程序

这是一个简化版的测试程序，用于快速验证服务器的基本功能。

使用方法：
1. 启动服务端（manager.py）
2. 运行此程序：python quick_test.py
3. 在管理界面创建并开始比赛
4. 观察测试结果

作者：AI Assistant
日期：2025-06-18
"""

import asyncio
import socketio
import random
import time
import argparse
from datetime import datetime

class QuickTestClient:
    """快速测试客户端"""
    
    def __init__(self, client_id: int, server_url: str):
        self.client_id = client_id
        self.server_url = server_url
        self.sio = socketio.AsyncClient()
        self.nickname = self.generate_nickname()
        self.connected = False
        self.registered = False
        self.contest_started = False
        self.contest_text = ""
        self.typing_speed = random.uniform(40, 70)  # 字符/分钟
        self.error_rate = random.uniform(0.05, 0.12)  # 错误率
        self.correct_count = 0
        self.error_count = 0
        self.setup_events()
        
    def generate_nickname(self):
        """生成随机昵称"""
        prefixes = ["测试", "虚拟", "模拟", "自动", "机器"]
        suffixes = ["用户", "客户端", "玩家", "选手", "参赛者"]
        return f"{random.choice(prefixes)}{random.choice(suffixes)}{self.client_id}"
        
    def setup_events(self):
        """设置事件处理器"""
        
        @self.sio.event
        async def connect():
            print(f"[客户端{self.client_id}] 连接成功")
            self.connected = True
            await self.register()
            
        @self.sio.event
        async def connect_error(data):
            print(f"[客户端{self.client_id}] 连接失败: {data}")
            
        @self.sio.event
        async def registration_success(data):
            print(f"[客户端{self.client_id}] 注册成功: {data['user']['nickname']}")
            self.registered = True
            
            # 如果比赛已经开始，立即开始打字
            if data.get('contest') and data['contest'].get('status') == 'running':
                await self.start_typing(data['contest']['text_content'])
                
        @self.sio.event
        async def contest_status_update(data):
            status = data.get('status')
            print(f"[客户端{self.client_id}] 比赛状态: {status}")
            
            if status == 'running' and not self.contest_started:
                # 获取比赛文本（这里使用模拟文本）
                test_text = "hello world welcome to typing game good morning nice to meet you"
                await self.start_typing(test_text)
                
        @self.sio.event
        async def leaderboard_update(data):
            # 可以在这里处理排行榜更新
            pass
            
    async def connect_to_server(self):
        """连接到服务器"""
        try:
            await self.sio.connect(self.server_url)
            return True
        except Exception as e:
            print(f"[客户端{self.client_id}] 连接失败: {e}")
            return False
            
    async def register(self):
        """注册用户"""
        try:
            await self.sio.emit('register_user', {'nickname': self.nickname})
        except Exception as e:
            print(f"[客户端{self.client_id}] 注册失败: {e}")
            
    async def start_typing(self, text):
        """开始打字模拟"""
        if self.contest_started:
            return
            
        print(f"[客户端{self.client_id}] 开始打字模拟")
        self.contest_started = True
        self.contest_text = text
        
        # 启动打字任务
        asyncio.create_task(self.simulate_typing())
        
    async def simulate_typing(self):
        """模拟打字过程"""
        try:
            text_length = len(self.contest_text)
            chars_per_second = self.typing_speed / 60.0
            
            for i in range(text_length):
                # 计算延迟
                delay = 1.0 / chars_per_second
                delay *= random.uniform(0.8, 1.2)  # 添加随机性
                
                await asyncio.sleep(delay)
                
                # 模拟错误
                if random.random() < self.error_rate:
                    self.error_count += 1
                else:
                    self.correct_count += 1
                    
                # 每10个字符发送一次进度
                if (i + 1) % 10 == 0 or i == text_length - 1:
                    await self.send_progress(i == text_length - 1)
                    
            print(f"[客户端{self.client_id}] 完成打字")
            
        except Exception as e:
            print(f"[客户端{self.client_id}] 打字模拟失败: {e}")
            
    async def send_progress(self, completed=False):
        """发送打字进度"""
        try:
            await self.sio.emit('typing_progress', {
                'correct_count': self.correct_count,
                'error_count': self.error_count,
                'completed': completed
            })
        except Exception as e:
            print(f"[客户端{self.client_id}] 发送进度失败: {e}")
            
    async def disconnect(self):
        """断开连接"""
        try:
            await self.sio.disconnect()
        except Exception as e:
            print(f"[客户端{self.client_id}] 断开连接失败: {e}")

class QuickTestManager:
    """快速测试管理器"""
    
    def __init__(self, server_url: str, num_clients: int):
        self.server_url = server_url
        self.num_clients = num_clients
        self.clients = []
        
    async def run_test(self):
        """运行测试"""
        print(f"开始快速测试 - {self.num_clients} 个客户端")
        print("=" * 50)
        
        # 创建客户端
        for i in range(self.num_clients):
            client = QuickTestClient(i + 1, self.server_url)
            self.clients.append(client)
            
        # 连接客户端
        print("正在连接客户端...")
        connected_count = 0
        
        for client in self.clients:
            if await client.connect_to_server():
                connected_count += 1
            await asyncio.sleep(0.1)  # 避免连接过快
            
        print(f"连接完成: {connected_count}/{self.num_clients}")
        
        if connected_count == 0:
            print("没有客户端连接成功，测试结束")
            return
            
        # 等待用户开始比赛
        print("\n请在管理界面中：")
        print("1. 设置比赛文本")
        print("2. 点击'创建比赛'")
        print("3. 点击'开始比赛'")
        print("\n等待比赛开始...")
        
        # 等待比赛开始
        while True:
            contest_started = any(client.contest_started for client in self.clients)
            if contest_started:
                print("检测到比赛开始！")
                break
            await asyncio.sleep(1)
            
        # 等待比赛完成
        print("等待比赛完成...")
        start_time = time.time()
        timeout = 120  # 2分钟超时
        
        while time.time() - start_time < timeout:
            # 检查完成状态
            registered_clients = [c for c in self.clients if c.registered]
            if not registered_clients:
                break
                
            # 简单等待
            await asyncio.sleep(2)
            
        # 断开所有连接
        print("断开所有连接...")
        for client in self.clients:
            await client.disconnect()
            
        # 生成简单报告
        self.generate_simple_report()
        
    def generate_simple_report(self):
        """生成简单报告"""
        print("\n" + "=" * 50)
        print("快速测试报告")
        print("=" * 50)
        
        connected_count = sum(1 for c in self.clients if c.connected)
        registered_count = sum(1 for c in self.clients if c.registered)
        started_count = sum(1 for c in self.clients if c.contest_started)
        
        print(f"总客户端数: {self.num_clients}")
        print(f"成功连接: {connected_count}")
        print(f"成功注册: {registered_count}")
        print(f"开始比赛: {started_count}")
        
        if started_count > 0:
            avg_correct = sum(c.correct_count for c in self.clients if c.contest_started) / started_count
            avg_errors = sum(c.error_count for c in self.clients if c.contest_started) / started_count
            print(f"平均正确字符: {avg_correct:.1f}")
            print(f"平均错误字符: {avg_errors:.1f}")
            
        print("=" * 50)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='在线打字比赛系统 - 快速测试')
    parser.add_argument('--server', '-s', default='http://localhost:5000',
                       help='服务器地址')
    parser.add_argument('--clients', '-c', type=int, default=10,
                       help='客户端数量')
    
    args = parser.parse_args()
    
    print("在线打字比赛系统 - 快速测试程序")
    print(f"服务器: {args.server}")
    print(f"客户端数量: {args.clients}")
    print()
    
    # 运行测试
    test_manager = QuickTestManager(args.server, args.clients)
    
    try:
        asyncio.run(test_manager.run_test())
    except KeyboardInterrupt:
        print("\n测试被中断")
    except Exception as e:
        print(f"\n测试失败: {e}")
        
    print("测试完成！")

if __name__ == "__main__":
    main()
