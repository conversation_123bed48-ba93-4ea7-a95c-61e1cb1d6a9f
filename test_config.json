{"test_settings": {"server_url": "http://localhost:5000", "default_clients": 60, "quick_test_clients": 10, "connection_timeout": 10, "test_timeout": 300, "batch_size": 10, "batch_delay": 0.5}, "client_profiles": [{"name": "slow_typist", "typing_speed_range": [20, 35], "error_rate_range": [0.15, 0.25], "weight": 0.2}, {"name": "average_typist", "typing_speed_range": [35, 60], "error_rate_range": [0.08, 0.15], "weight": 0.6}, {"name": "fast_typist", "typing_speed_range": [60, 90], "error_rate_range": [0.03, 0.08], "weight": 0.2}], "test_scenarios": [{"name": "basic_load_test", "description": "基础负载测试", "clients": 30, "duration": 120}, {"name": "stress_test", "description": "压力测试", "clients": 60, "duration": 300}, {"name": "peak_load_test", "description": "峰值负载测试", "clients": 100, "duration": 180}], "monitoring": {"performance_interval": 5, "log_level": "INFO", "save_detailed_logs": true, "monitor_system_resources": true}, "test_data": {"sample_texts": ["hello world welcome to typing game good morning nice to meet you today is a beautiful day let us practice typing together", "the quick brown fox jumps over the lazy dog this is a common sentence used for typing practice", "在线打字比赛系统测试文本，包含中英文混合内容，用于验证系统的多语言支持能力。", "Python is a powerful programming language that is widely used in web development data science and artificial intelligence"]}, "expected_performance": {"max_cpu_usage": 80, "max_memory_usage": 70, "min_connection_success_rate": 95, "max_response_time": 1000, "min_server_uptime": 99}}