('D:\\testcode\\typegame改\\build\\TypingContest\\TypingContest.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\testcode\\typegame改\\build\\TypingContest\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\testcode\\typegame改\\build\\TypingContest\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\testcode\\typegame改\\build\\TypingContest\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\testcode\\typegame改\\build\\TypingContest\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\testcode\\typegame改\\build\\TypingContest\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\testcode\\typegame改\\build\\TypingContest\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'd:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'D:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'D:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_traitlets',
   'D:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_traitlets.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'D:\\Program '
   'Files\\miniconda3\\envs\\test3.10\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('manager', 'D:\\testcode\\typegame改\\server\\manager.py', 'PYSOURCE')],
 'python310.dll',
 True,
 False,
 False,
 [],
 None,
 None,
 None)
