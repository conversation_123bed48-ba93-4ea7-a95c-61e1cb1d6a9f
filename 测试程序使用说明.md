# 在线打字比赛系统 - 自动化测试程序使用说明

## 概述

本测试套件包含多个测试工具，用于验证在线打字比赛系统在高并发情况下的性能和稳定性。

## 文件说明

### 主要测试程序

1. **test_concurrent_clients.py** - 完整的并发测试程序
   - 模拟60个虚拟客户端
   - 完整的性能监控和报告
   - 详细的统计分析

2. **quick_test.py** - 快速测试程序
   - 轻量级测试工具
   - 适合快速验证基本功能
   - 默认10个客户端

3. **performance_monitor.py** - 性能监控工具
   - 实时监控系统资源
   - 服务器响应性检测
   - 生成性能报告

### 配置文件

4. **test_config.json** - 测试配置文件
   - 测试参数配置
   - 客户端行为配置
   - 性能基准设置

## 环境要求

### Python依赖

```bash
pip install python-socketio[asyncio] psutil requests
```

### 系统要求

- Python 3.7+
- 至少2GB可用内存
- 稳定的网络连接

## 使用步骤

### 1. 准备工作

1. 确保服务端已启动：
   ```bash
   python server/manager.py
   ```

2. 在管理界面中点击"启动服务器"

3. 记录服务器地址（通常是 http://localhost:5000）

### 2. 运行测试

#### 快速测试（推荐新手）

```bash
# 基础测试（10个客户端）
python quick_test.py

# 指定客户端数量
python quick_test.py --clients 20

# 指定服务器地址
python quick_test.py --server http://*************:5000
```

#### 完整并发测试

```bash
# 默认60个客户端
python test_concurrent_clients.py

# 自定义参数
python test_concurrent_clients.py --clients 100 --server http://localhost:5000 --verbose
```

#### 性能监控

```bash
# 基础监控
python performance_monitor.py

# 自定义监控间隔和输出
python performance_monitor.py --interval 3 --output performance.log --server http://localhost:5000
```

### 3. 操作流程

1. **启动测试程序**
   - 程序会自动连接虚拟客户端
   - 等待所有客户端连接完成

2. **在管理界面操作**
   - 设置比赛文本内容
   - 点击"创建比赛"
   - 点击"开始比赛"

3. **观察测试过程**
   - 虚拟客户端自动开始"打字"
   - 实时显示连接状态和进度
   - 监控系统性能指标

4. **查看测试结果**
   - 测试完成后自动生成报告
   - 检查性能指标和错误信息
   - 分析服务器稳定性

## 测试参数说明

### 命令行参数

#### test_concurrent_clients.py

- `--server, -s`: 服务器地址（默认：http://localhost:5000）
- `--clients, -c`: 客户端数量（默认：60）
- `--verbose, -v`: 详细输出模式

#### quick_test.py

- `--server, -s`: 服务器地址（默认：http://localhost:5000）
- `--clients, -c`: 客户端数量（默认：10）

#### performance_monitor.py

- `--interval, -i`: 监控间隔秒数（默认：5）
- `--output, -o`: 输出文件路径
- `--server, -s`: 服务器地址（默认：http://localhost:5000）

### 配置文件参数

编辑 `test_config.json` 可以调整：

- 客户端打字速度范围
- 错误率设置
- 测试超时时间
- 性能基准值

## 测试报告解读

### 连接统计

- **连接成功率**: 应该 > 95%
- **平均连接时间**: 应该 < 2秒
- **注册成功率**: 应该 = 100%

### 性能指标

- **CPU使用率**: 应该 < 80%
- **内存使用率**: 应该 < 70%
- **服务器响应率**: 应该 > 99%

### 比赛统计

- **完成率**: 应该 > 90%
- **平均得分**: 根据文本长度和错误率计算
- **排行榜更新**: 应该实时更新

## 常见问题

### 1. 连接失败

**问题**: 大量客户端连接失败

**解决方案**:
- 检查服务器是否正常启动
- 确认防火墙设置
- 减少并发连接数量
- 增加连接超时时间

### 2. 性能问题

**问题**: 服务器CPU或内存使用率过高

**解决方案**:
- 减少客户端数量
- 检查服务器硬件配置
- 优化数据库查询
- 调整Socket.IO配置

### 3. 测试中断

**问题**: 测试过程中程序崩溃

**解决方案**:
- 检查错误日志
- 确保网络连接稳定
- 使用 `--verbose` 模式获取详细信息
- 尝试快速测试验证基本功能

### 4. 数据不一致

**问题**: 排行榜数据不准确

**解决方案**:
- 检查数据库连接
- 验证Socket.IO事件处理
- 确认客户端发送的数据格式
- 检查服务端计分逻辑

## 测试最佳实践

### 1. 渐进式测试

- 先用快速测试验证基本功能
- 逐步增加客户端数量
- 监控系统资源使用情况

### 2. 多场景测试

- 测试不同文本长度
- 测试中英文混合内容
- 测试异常断线情况

### 3. 长期稳定性测试

- 运行较长时间的测试
- 监控内存泄漏
- 检查连接池状态

### 4. 压力测试

- 超过设计容量的客户端数量
- 快速连接和断开
- 异常数据输入测试

## 技术支持

如果遇到问题，请：

1. 查看测试日志文件
2. 检查服务器日志
3. 使用性能监控工具分析
4. 参考配置文件示例

## 更新日志

- v1.0: 初始版本，支持基本并发测试
- v1.1: 添加性能监控功能
- v1.2: 增加快速测试工具
- v1.3: 完善错误处理和报告生成
